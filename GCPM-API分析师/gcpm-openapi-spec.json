{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/visits/reserve/from-goldata": {"post": {"operationId": "VisitsController_createVisitReserve", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVisitReserveRequestDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "从Goldata创建访视预约", "tags": ["访视"]}}, "/visits/complete-info-record/from-goldata": {"post": {"operationId": "VisitsController_createVisitCompleteRecord", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVisitCompleteRecordRequestDto"}}}}, "responses": {"201": {"description": "信息记录创建成功"}, "400": {"description": "无效数据"}}, "summary": "从Goldata创建完整信息记录", "tags": ["访视"]}}, "/visits/complete-info-record/from-gcpm-frontend": {"post": {"operationId": "VisitsController_createVisitCompleteRecordFromGCPMFrontend", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinishVisitCreateDto"}}}}, "responses": {"201": {"description": "结束访视记录创建成功"}, "400": {"description": "无效数据"}}, "summary": "从 gcpm-frontend 创建结束访视记录", "tags": ["访视"]}}, "/visits/complete-info-record/history": {"get": {"operationId": "VisitsController_getHistoryVisitCompleteRecords", "parameters": [{"name": "encrypted_reserve_infos", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "user_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取所有历史结束访视记录", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitCompleteRecords"}}}}}}, "summary": "获取历史结束访视记录", "tags": ["访视"]}}, "/visits/complete-info-record/current-history": {"get": {"operationId": "VisitsController_getCurrentHistoryVisitCompleteRecord", "parameters": [{"name": "encrypted_reserve_infos", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "user_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取最新历史结束访视记录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitCompleteRecords"}}}}}, "summary": "获取最新的历史结束访视记录", "tags": ["访视"]}}, "/visits": {"get": {"operationId": "VisitsController_findAll", "parameters": [], "responses": {"200": {"description": "成功获取所有访视"}}, "summary": "获取所有访视", "tags": ["访视"]}}, "/visits/{id}": {"get": {"operationId": "VisitsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取访视", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitReserveResponseDto"}}}}}, "summary": "通过ID获取访视", "tags": ["访视"]}, "patch": {"operationId": "VisitsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVisitDto"}}}}, "responses": {"200": {"description": "访视更新成功", "content": {"application/json": {"schema": {"type": "string"}}}}}, "summary": "更新访视", "tags": ["访视"]}, "delete": {"operationId": "VisitsController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "访视删除成功", "content": {"application/json": {"schema": {"type": "string"}}}}}, "summary": "删除访视", "tags": ["访视"]}}, "/visits/reserve/date/{date}": {"get": {"operationId": "VisitsController_findByReserveDate", "parameters": [{"name": "date", "required": true, "in": "path", "description": "日期格式: YYYY-MM-DD", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取访视预约", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VisitReserveResponseDto"}}}}}}, "summary": "通过日期获取访视预约", "tags": ["访视"]}}, "/visits/reserve/department-staff": {"get": {"operationId": "VisitsController_getDepartmentStaff", "parameters": [], "responses": {"200": {"description": "成功获取科室人员信息表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentStaff"}}}}}}, "summary": "获取科室人员信息表", "tags": ["访视"]}}, "/visits/reserve/is-visit-complete": {"get": {"operationId": "VisitsController_isVisitComplete", "parameters": [{"name": "reserveID", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取访视预约记录是否已经登记过结束", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsVisitCompleteDto"}}}}}, "summary": "查询一条访视预约记录是否已经登记过结束", "tags": ["访视"]}}, "/visits/reserve": {"post": {"operationId": "VisitsController_createVisitReserveFromClinicalData", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicalDataVisitReserveCreateRequestDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "创建访视预约", "tags": ["访视"]}}, "/visits/complete-info-record/mbgl-sync": {"post": {"operationId": "VisitsController_syncFinishVisitToMBGL", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinishVisitCreateDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "同步访视登记到慢病管理系统", "tags": ["访视"]}}, "/visits/complete-info-record/mbgl-check": {"post": {"operationId": "VisitsController_checkVisitFormValid", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinishVisitMbglCheckDto"}}}}, "responses": {"200": {"description": "访视登记表单是合法的慢病管理系统表单（如果成功恒为 true，失败会抛出异常）"}}, "summary": "检查访视登记表单是否是合法的慢病管理系统表单", "tags": ["访视"]}}, "/visits/reserve/patient-state": {"post": {"operationId": "VisitsController_getPatientState", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveVisitPatientStateDto"}}}}, "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "summary": "获取患者状态", "tags": ["访视"]}}, "/visits/reserve/{id}": {"patch": {"operationId": "VisitsController_updateVisitReserve", "parameters": [{"name": "id", "required": true, "in": "path", "description": "访视预约ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVisitReserveDto"}}}}, "responses": {"200": {"description": "访视预约更新成功"}}, "summary": "更新访视预约", "tags": ["访视"]}, "get": {"operationId": "VisitsController_getVisitReserveDetail", "parameters": [{"name": "id", "required": true, "in": "path", "description": "访视预约ID", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitReserveResponseDto"}}}}}, "summary": "获取一个访视预约的详细信息", "tags": ["访视"]}}, "/visits/reserve/{id}/check-mbgl-sync": {"get": {"operationId": "VisitsController_checkVisitReserveSyncToMBGL", "parameters": [{"name": "id", "required": true, "in": "path", "description": "访视预约ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "检查访视预约是否登记过，以及是否登记/同步成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveCheckMBGLSyncResultDto"}}}}}, "summary": "检查访视预约是否登记过，以及是否登记/同步成功", "tags": ["访视"]}}, "/visits/reserve/{id}/resync-mbgl": {"get": {"operationId": "VisitsController_resyncVisitReserveToMBGL", "parameters": [{"name": "id", "required": true, "in": "path", "description": "访视预约ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "重新同步访视预约到慢病管理系统"}}, "summary": "重新同步访视预约到慢病管理系统", "tags": ["访视"]}}, "/visits/reserve/crc": {"post": {"operationId": "VisitsController_crcCreateReserveVisit", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrcCreateReserveVisitDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "CRC访视预约的创建", "tags": ["访视"]}}, "/visits/reserve/analyzer/total": {"get": {"operationId": "VisitsController_getVisitTotal", "parameters": [{"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取访视预约总量成功", "content": {"application/json": {"schema": {"type": "number"}}}}}, "summary": "获取访视预约总量", "tags": ["访视"]}}, "/patients": {"post": {"operationId": "PatientsController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientCreateRequestDto"}}}}, "responses": {"409": {"description": "同名和同电话的患者在GCPM中已经存在"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientCreateResponseDto"}}}}}, "summary": "创建患者", "tags": ["患者管理"]}, "get": {"operationId": "PatientsController_query", "parameters": [{"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PatientQueryResponseDto"}}}}}}, "summary": "查找患者", "tags": ["患者管理"]}}, "/patients/patients/{patientId}/mbgl-sync": {"post": {"operationId": "PatientsController_syncToMbgl", "parameters": [{"name": "patientId", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MbglPatientCreateRequestDto"}}}}}, "summary": "同步 GCPM 患者到慢病管理系统中", "tags": ["患者管理"]}}, "/patients/pagination": {"get": {"operationId": "PatientsController_queryWithPagination", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "每页条数", "schema": {"default": 10, "type": "number"}}, {"name": "cursor", "required": false, "in": "query", "description": "游标(患者ID)", "schema": {"type": "string"}}, {"name": "name", "required": false, "in": "query", "description": "患者姓名(可选过滤)", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "description": "患者电话(可选过滤)", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientPaginationResponseDto"}}}}}, "summary": "分页查询患者", "tags": ["患者管理"]}}, "/patients/{id}": {"get": {"operationId": "PatientsController_getById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientQueryResponseDto"}}}}}, "summary": "根据ID获取患者详情", "tags": ["患者管理"]}, "patch": {"operationId": "PatientsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientUpdateRequestDto"}}}}, "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientUpdateResponseDto"}}}}}, "summary": "更新患者信息", "tags": ["患者管理"]}, "delete": {"operationId": "PatientsController_softDelete", "parameters": [{"name": "id", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientUpdateResponseDto"}}}}}, "summary": "删除患者(软删除)", "tags": ["患者管理"]}}, "/patients/{id}/restore": {"post": {"operationId": "PatientsController_restore", "parameters": [{"name": "id", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientUpdateResponseDto"}}}}}, "summary": "恢复已删除的患者", "tags": ["患者管理"]}}, "/patients/deleted": {"get": {"operationId": "PatientsController_queryDeletedWithPagination", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "每页条数", "schema": {"default": 10, "type": "number"}}, {"name": "cursor", "required": false, "in": "query", "description": "游标(患者ID)", "schema": {"type": "string"}}, {"name": "name", "required": false, "in": "query", "description": "患者姓名(可选过滤)", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "description": "患者电话(可选过滤)", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientPaginationResponseDto"}}}}}, "summary": "查询已删除的患者", "tags": ["患者管理"]}}, "/patients/{id}/diseases": {"get": {"operationId": "PatientsController_getPatientDiseases", "parameters": [{"name": "id", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "number"}}}}}}, "summary": "获取患者疾病列表", "tags": ["患者管理"]}}, "/mbgl/patient": {"get": {"operationId": "MbglPatientController_query", "parameters": [{"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MbglPatientQueryResponseDto"}}}}}}, "summary": "查询慢病管理系统中的患者", "tags": ["慢病管理系统患者"]}}, "/mbgl/patient/{patientId}": {"get": {"operationId": "MbglPatientController_getPatientById", "parameters": [{"name": "patientId", "required": true, "in": "path", "description": "患者ID", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MbglPatientQueryResponseDto"}}}}}, "summary": "根据 Id 返回某个患者的详细信息", "tags": ["慢病管理系统患者"]}}, "/mbgl/patient/report/total": {"get": {"operationId": "MbglPatientController_queryTotalPatients", "parameters": [{"name": "startDate", "required": true, "in": "query", "description": "开始日期", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "description": "结束日期", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MbglSysRecommendPatientResponseDto"}}}}}}, "summary": "获取某个时间段内新增的患者", "tags": ["慢病管理系统患者"]}}, "/mbgl/project": {"get": {"operationId": "MbglProjectController_query", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MbglProjectQueryResponseDto"}}}}}}, "summary": "查询慢病管理系统中的项目", "tags": ["慢病管理系统项目"]}}, "/mbgl/project/{id}/patient-state/{patientId}": {"get": {"operationId": "MbglProjectController_getPatientStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "patientId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "summary": "获取项目中某个患者当前的受试状态", "tags": ["慢病管理系统项目"]}}, "/mbgl/project/{id}": {"get": {"operationId": "MbglProjectController_getProjectInfo", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MbglProjectQueryResponseDto"}}}}}, "summary": "获取慢病管理系统中的项目信息", "tags": ["慢病管理系统项目"]}}, "/mbgl/disease/{id}": {"get": {"operationId": "MbglDiseaseController_getDiseaseById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "疾病ID", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MbglDiseaseQueryResponseDto"}}}}}, "summary": "根据疾病ID获取疾病信息", "tags": ["慢病管理系统疾病"]}}, "/mbgl-users": {"post": {"operationId": "MbglSystemUsersController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMbglSystemUsersDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["MBGLSystemUsers"]}, "get": {"operationId": "MbglSystemUsersController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["MBGLSystemUsers"]}}, "/mbgl-users/{id}": {"get": {"operationId": "MbglSystemUsersController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["MBGLSystemUsers"]}, "patch": {"operationId": "MbglSystemUsersController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMbglSystemUsersDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["MBGLSystemUsers"]}, "delete": {"operationId": "MbglSystemUsersController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["MBGLSystemUsers"]}}, "/mbgl-users/name/{name}/phone/{phone}": {"get": {"operationId": "MbglSystemUsersController_findOneWithNameAndPhone", "parameters": [{"name": "name", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "phone", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["MBGLSystemUsers"]}}, "/mbgl-users/{id}/visit-complete-records": {"get": {"operationId": "MbglSystemUsersController_findVisitCompleteRecordsWithUserId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["MBGLSystemUsers"]}}, "/schedule/user-sync-to-goldata": {"post": {"operationId": "ScheduleController_syncUsers", "parameters": [{"name": "start", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "end", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Schedule"]}}, "/schedule/visit-record": {"get": {"operationId": "ScheduleController_visitRecord", "parameters": [{"name": "start_date", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "end_date", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Schedule"]}}, "/clinical-events/from-goldata": {"post": {"operationId": "ClinicalEventsController_fromGoldata", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClinicalEventDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["临床事件"]}}, "/clinical-events/patient/name/{name}": {"get": {"operationId": "ClinicalEventsController_getClinicalEventsWithUserName", "parameters": [{"name": "name", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["临床事件"]}}, "/clinical-events/patient/id/{id}": {"get": {"operationId": "ClinicalEventsController_getClinicalEventWithUserId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "project_code", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "order", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["临床事件"]}}, "/clinical-events/fix-no-user-id": {"post": {"operationId": "ClinicalEventsController_fixNoUserId", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["临床事件"]}}, "/clinical-device": {"get": {"operationId": "ClinicalDeviceController_getClinicalDevices", "parameters": [{"name": "skip", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "take", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "orderBy", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "获取所有临床设备", "tags": ["临床设备"]}}, "/clinical-device/{id}": {"get": {"operationId": "ClinicalDeviceController_getClinicalDeviceById", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "根据ID获取临床设备", "tags": ["临床设备"]}, "put": {"operationId": "ClinicalDeviceController_updateClinicalDevice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "更新临床设备信息", "tags": ["临床设备"]}}, "/clinical-device/event": {"post": {"operationId": "ClinicalDeviceController_createClinicalDeviceEvent", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClinicalDeviceEventDto"}}}}, "responses": {"200": {"description": "创建临床设备事件成功"}}, "summary": "创建临床设备事件", "tags": ["临床设备"]}}, "/clinical-device/event/from-goldata": {"post": {"operationId": "ClinicalDeviceController_createClinicalDeviceEventFromGoldata", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClinicalDeviceEventRequestDto"}}}}, "responses": {"200": {"description": "从金数据添加临床设备事件成功"}, "400": {"description": "从金数据添加临床设备事件失败"}}, "summary": "从金数据添加临床设备事件", "tags": ["临床设备"]}}, "/clinical-device/binding/from-goldata": {"post": {"operationId": "ClinicalDeviceController_createClinicalDeviceBindingFromGoldata", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClinicalDeviceBindingRequestDto"}}}}, "responses": {"200": {"description": "从金数据添加临床设备绑定成功"}, "400": {"description": "从金数据添加临床设备绑定失败"}}, "summary": "从金数据添加临床设备绑定", "tags": ["临床设备"]}}, "/clinical-device/from-goldata": {"post": {"operationId": "ClinicalDeviceController_createClinicalDeviceFromGoldata", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClinicalDeviceRequestDto"}}}}, "responses": {"200": {"description": "从金数据注册临床设备成功"}, "400": {"description": "从金数据注册临床设备失败"}}, "summary": "从金数据注册临床设备", "tags": ["临床设备"]}}, "/research-projects/all-projects": {"get": {"operationId": "ResearchProjectsController_getAllResearchProjects", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有研究项目", "tags": ["研究项目列表"]}}, "/research-projects/project-by-id": {"get": {"operationId": "ResearchProjectsController_getProjectByID", "parameters": [{"name": "project_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "获取对应 ID 的项目", "tags": ["研究项目列表"]}}, "/research-projects/all-project-statuses": {"get": {"operationId": "ResearchProjectsController_getAllResearchProjectStatuses", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有项目状态", "tags": ["研究项目列表"]}}, "/research-projects/all-project-phases": {"get": {"operationId": "ResearchProjectsController_getAllResearchProjectPhases", "parameters": [{"name": "order_by", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "获取所有项目研究期数", "tags": ["研究项目列表"]}}, "/research-projects/all-research-sponsors": {"get": {"operationId": "ResearchProjectsController_getAllResearchSponsors", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有研究申办方", "tags": ["研究项目列表"]}}, "/research-projects/all-projects-members": {"get": {"operationId": "ResearchProjectsController_getAllProjectsMembers", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有项目人员", "tags": ["研究项目列表"]}}, "/research-projects/project-members": {"get": {"operationId": "ResearchProjectsController_getProjectMembers", "parameters": [{"name": "project_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "获取对应项目下的所有人员", "tags": ["研究项目列表"]}}, "/research-projects/all-project-role-types": {"get": {"operationId": "ResearchProjectsController_getAllProjectRoleTypes", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有项目成员类型", "tags": ["研究项目列表"]}}, "/research-projects/project-diseases": {"get": {"operationId": "ResearchProjectsController_getProjectDiseases", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有项目疾病对应表", "tags": ["研究项目列表"]}}, "/research-projects/all-diseases": {"get": {"operationId": "ResearchProjectsController_getAllDiseases", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有研究疾病", "tags": ["研究项目列表"]}}, "/research-projects/status-with-id": {"get": {"operationId": "ResearchProjectsController_getResearchProjectStatusWithID", "parameters": [{"name": "status_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "通过 statusID 获取对应的 ResearchProjectStatus", "tags": ["研究项目列表"]}}, "/research-projects/create-project-filing": {"post": {"operationId": "ResearchProjectsController_createProjectFiling", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectFilingDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "创建项目备案表单", "tags": ["研究项目列表"]}}, "/research-projects/all-research-object-types": {"get": {"operationId": "ResearchProjectsController_getAllResearchObjectTypes", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有研究对象类型", "tags": ["研究项目列表"]}}, "/research-projects/research-project": {"get": {"operationId": "ResearchProjectsController_getResearchProject", "parameters": [{"name": "project_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "获取一个研究项目", "tags": ["研究项目列表"]}}, "/research-projects/{projectID}/event-statistics": {"get": {"operationId": "ResearchProjectsController_getClinicalEventStatistics", "parameters": [{"name": "projectID", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "获取研究项目临床事件统计数据", "tags": ["研究项目列表"]}}, "/research-projects/{projectID}/update-project-basic-info": {"post": {"operationId": "ResearchProjectsController_updateProjectBasicInfo", "parameters": [{"name": "projectID", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResearchProjectEditBasicInfoDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "编辑项目基本信息", "tags": ["研究项目列表"]}}, "/research-projects/temp-jsj-projects": {"get": {"operationId": "ResearchProjectsController_getTempJSJProjects", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取金数据项目列表", "tags": ["研究项目列表"]}}, "/research-projects/mbgl-projects": {"get": {"operationId": "ResearchProjectsController_getMbglProjects", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取慢病管理系统的项目列表", "tags": ["研究项目列表"]}}, "/research-projects/{projectID}/sync-to-mbgl": {"post": {"operationId": "ResearchProjectsController_syncResearchProjectsToMbgl", "parameters": [{"name": "projectID", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "summary": "同步研究项目到慢病管理系统中", "tags": ["研究项目列表"]}}, "/research-projects/subjects/{id}/subjects": {"get": {"operationId": "SubjectController_getSubjectsByProjectId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subject"}}}}}}, "summary": "获取研究项目下的所有受试者", "tags": ["研究项目列表"]}}, "/research-projects/subjects/{id}/subject/{subjectId}/events": {"get": {"operationId": "SubjectController_getSubjectEvents", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "subjectId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubjectEvent"}}}}}}, "summary": "获取一位受试者的所有事件", "tags": ["研究项目列表"]}}, "/research-projects/subjects/{id}/patient": {"get": {"operationId": "SubjectController_getPatientBySubjectId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientQueryResponseDto"}}}}}, "summary": "通过受试者 Id 获取患者信息", "tags": ["研究项目列表"]}}, "/research-projects/subjects/{id}/project": {"get": {"operationId": "SubjectController_getProjectBySubjectId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectQueryResponseDto"}}}}}, "summary": "通过受试者 Id 获取研究项目信息", "tags": ["研究项目列表"]}}, "/research-projects/subjects/analyzer/subject-event": {"get": {"operationId": "SubjectController_getSubjectEventTotal", "parameters": [{"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "subjectEventTypeId", "required": false, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取受试者事件总量成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubjectEventQueryDto"}}}}}}, "summary": "获取受试者事件总量", "tags": ["研究项目列表"]}}, "/team-management/{userID}": {"delete": {"operationId": "TeamManagementController_removeTeamMember", "parameters": [{"name": "userID", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "删除团队成员", "tags": ["团队管理"]}, "put": {"operationId": "TeamManagementController_updateTeamMemberInfo", "parameters": [{"name": "userID", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTeamMemberInfoDto"}}}}, "responses": {"200": {"description": ""}}, "summary": "更新成员信息", "tags": ["团队管理"]}}, "/team-management/all-user-profile": {"get": {"operationId": "TeamManagementController_getAllUserProfile", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有用户基本信息", "tags": ["团队管理"]}}, "/team-management/all-department": {"get": {"operationId": "TeamManagementController_getAllDepartment", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有部门", "tags": ["团队管理"]}}, "/team-management/all-status": {"get": {"operationId": "TeamManagementController_getAllStatus", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有在职状态", "tags": ["团队管理"]}}, "/team-management/all-department-member": {"get": {"operationId": "TeamManagementController_getAllDepartmentMember", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "获取所有部门成员", "tags": ["团队管理"]}}, "/team-management/add-member": {"post": {"operationId": "TeamManagementController_addTeamMember", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTeamMemberDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "添加团队成员", "tags": ["团队管理"]}}, "/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}, "401": {"description": "身份验证失败(用户名或密码错误)"}, "404": {"description": "未找到用户"}}, "summary": "登录", "tags": ["auth"]}}, "/auth/register": {"post": {"operationId": "AuthController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequestDto"}}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponseDto"}}}}, "409": {"description": "用户已存在"}}, "summary": "注册用户", "tags": ["auth"]}}, "/user": {"get": {"operationId": "UserController_getUsers", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetUserDetailResponseDto"}}}}}}, "summary": "获取所有 CRC 用户", "tags": ["User"]}}, "/user/judgment-doctors": {"get": {"operationId": "UserController_getJudgmentDoctors", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetUserDetailResponseDto"}}}}}}, "summary": "获取所有判定医生", "tags": ["User"]}}, "/user/crc/whitelist": {"get": {"operationId": "CRCWhitelistController_getWhitelistUserList", "parameters": [], "responses": {"200": {"description": "白名单用户列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CRCWhitelistQueryResponseDto"}}}}}}, "summary": "获取白名单用户列表", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "post": {"operationId": "CRCWhitelistController_createWhitelistUser", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CRCWhitelistCreateRequestDto"}}}}, "responses": {"200": {"description": "添加 CRC 到白名单成功"}}, "summary": "添加 CRC 到白名单", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/user/crc/whitelist/{id}": {"delete": {"operationId": "CRCWhitelistController_deleteWhitelistUser", "parameters": [{"name": "id", "required": true, "in": "path", "description": "CRC 的 ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "从白名单中删除 CRC 成功"}}, "summary": "从白名单中删除 CRC", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "patch": {"operationId": "CRCWhitelistController_updateWhitelistUser", "parameters": [{"name": "id", "required": true, "in": "path", "description": "CRC 的 ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CRCUpdateRequestDto"}}}}, "responses": {"200": {"description": "更新白名单中的 CRC 信息成功"}}, "summary": "更新白名单中的 CRC 信息", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "get": {"operationId": "CRCWhitelistController_getWhitelistUserDetail", "parameters": [{"name": "id", "required": true, "in": "path", "description": "CRC 的 ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取白名单中的 CRC 信息成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CRCWhitelistQueryResponseDto"}}}}}, "summary": "获取白名单中的 CRC 信息", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/reference-data/subject-states": {"get": {"operationId": "ReferenceDataController_getAllSubjectStates", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubjectState"}}}}}}, "summary": "获取所有受试者状态", "tags": ["引用数据"]}}, "/reference-data/subject-event-types": {"get": {"operationId": "ReferenceDataController_getAllSubjectEventTypes", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubjectEventTypeResponseDto"}}}}}}, "summary": "获取所有受试者事件类型", "tags": ["引用数据"]}}, "/reference-data/visit-types": {"get": {"operationId": "ReferenceDataController_getAllVisitTypes", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TempJSJVisitTypeResponseDto"}}}}}}, "summary": "获取所有访视类型", "tags": ["引用数据"]}}, "/reference-data/diseases": {"get": {"operationId": "ReferenceDataController_getAllDiseases", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetDiseaseResponseDto"}}}}}}, "summary": "获取所有疾病", "tags": ["引用数据"]}}, "/reference-data/research-project-phases": {"get": {"operationId": "ReferenceDataController_getAllResearchProjectPhases", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetProjectPhaseResponseDto"}}}}}}, "summary": "获取所有研究分期", "tags": ["引用数据"]}}, "/reference-data/research-project-statuses": {"get": {"operationId": "ReferenceDataController_getAllResearchProjectStatuses", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetProjectStatusResponseDto"}}}}}}, "summary": "获取所有研究状态", "tags": ["引用数据"]}}, "/reference-data/sponsors": {"get": {"operationId": "ReferenceDataController_getAllSponsors", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetSponsorResponseDto"}}}}}}, "summary": "获取所有申办方", "tags": ["引用数据"]}}, "/reference-data/object-type": {"get": {"operationId": "ReferenceDataController_getAllObjectType", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetObjectTypeResponseDto"}}}}}}, "summary": "获取所有研究对象类型", "tags": ["引用数据"]}}, "/reference-data/project-role-types": {"get": {"operationId": "ReferenceDataController_getAllProjectRoleTypes", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetProjectRoleTypeResponseDto"}}}}}}, "summary": "获取所有项目角色类型", "tags": ["引用数据"]}}, "/goldata": {"post": {"operationId": "GoldataController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGoldatumDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Goldata"]}, "get": {"operationId": "GoldataController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Goldata"]}}, "/goldata/{id}": {"get": {"operationId": "GoldataController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Goldata"]}, "patch": {"operationId": "GoldataController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGoldatumDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Goldata"]}, "delete": {"operationId": "GoldataController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Goldata"]}}, "/docs/create": {"post": {"operationId": "DocumentController_createDocument", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/CreateDocumentRequestDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["文档管理"]}}, "/docs/{id}": {"get": {"operationId": "DocumentController_getDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["文档管理"]}}, "/crc/reserve-visit": {"post": {"operationId": "CrcController_createReserveVisit", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReserveVisitRequestDto"}}}}, "responses": {"201": {"description": "访视预约创建成功"}, "400": {"description": "请求参数无效"}, "404": {"description": "用户不存在"}}, "tags": ["CRC工作面板"]}, "get": {"operationId": "CrcController_getReserveVisit", "parameters": [{"name": "id", "required": true, "in": "query", "description": "访视预约ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrcReserveVisitResponseDto"}}}}, "403": {"description": "无权访问该访视预约"}, "404": {"description": "访视预约或用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/reserve-visit/{id}": {"patch": {"operationId": "CrcController_updateReserveVisit", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReserveVisitRequestDto"}}}}, "responses": {"200": {"description": "访视预约更新成功"}, "400": {"description": "请求参数无效"}, "403": {"description": "无权访问该访视预约"}, "404": {"description": "访视预约或用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/reserve-visit/{id}/cancel": {"post": {"operationId": "CrcController_cancelReserveVisit", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelReserveVisitDto"}}}}, "responses": {"200": {"description": "访视预约取消成功"}, "403": {"description": "无权取消该访视预约"}, "404": {"description": "访视预约或用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/reserve-visit/pagination": {"get": {"operationId": "CrcController_getReserveVisitsWithPagination", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "每页条数", "schema": {"default": 10, "type": "number"}}, {"name": "cursor", "required": false, "in": "query", "description": "游标(访视预约ID)", "schema": {"type": "string"}}, {"name": "visitName", "required": false, "in": "query", "description": "访视名称(可选过滤)", "schema": {"type": "string"}}, {"name": "visitDate", "required": false, "in": "query", "description": "访视日期(可选过滤)", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveVisitPaginationResponseDto"}}}}, "404": {"description": "用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/quality-control/ae": {"post": {"operationId": "CrcController_createQualityControlAEEvent", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQualityControlAEEventRequestDto"}}}}, "responses": {"201": {"description": "质量控制 AE 事件创建成功"}, "400": {"description": "请求参数无效"}, "404": {"description": "用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/quality-control/sae": {"post": {"operationId": "CrcController_createQualityControlSAEEvent", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQualityControlSAEEventRequestDto"}}}}, "responses": {"201": {"description": "质量控制 SAE 事件创建成功"}, "400": {"description": "请求参数无效"}, "404": {"description": "用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/quality-control/pd": {"post": {"operationId": "CrcController_createQualityControlPDEvent", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQualityControlPDEventRequestDto"}}}}, "responses": {"201": {"description": "质量控制 PD 事件创建成功"}, "400": {"description": "请求参数无效"}, "404": {"description": "用户不存在"}}, "tags": ["CRC工作面板"]}}, "/crc/quality-control/events": {"get": {"operationId": "CrcController_getQualityControlEvents", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryQualityControlEventDto"}}}}}, "tags": ["CRC工作面板"]}}, "/crc/main-visit-content": {"get": {"operationId": "CrcController_getMainVisitContent", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}, "tags": ["CRC工作面板"]}}, "/guidance/reserve-visit/date/{date}": {"get": {"operationId": "GuidanceController_getReserveVisitsByDate", "parameters": [{"name": "date", "required": true, "in": "path", "description": "访视预约日期", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceQueryReserveVisitResponseDto"}}}}, "404": {"description": "用户不存在"}}, "tags": ["导诊工作面板"]}}, "/guidance/reserve-visit": {"get": {"operationId": "GuidanceController_getReserveVisitsById", "parameters": [{"name": "id", "required": true, "in": "query", "description": "访视预约 id", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceQueryReserveVisitResponseDto"}}}}, "404": {"description": "用户不存在"}}, "tags": ["导诊工作面板"]}, "post": {"operationId": "GuidanceController_createReserveVisit", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceCreateReserveVisitRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceQueryReserveVisitResponseDto"}}}}, "404": {"description": "用户不存在"}}, "tags": ["导诊工作面板"]}}, "/guidance/reserve-visit/unfinished": {"get": {"operationId": "GuidanceController_getUnfinishedReserveVisits", "parameters": [{"name": "date", "required": true, "in": "query", "schema": {"nullable": true, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceQueryReserveVisitResponseDto"}}}}, "404": {"description": "用户不存在"}}, "tags": ["导诊工作面板"]}}, "/guidance/reserve-visit/{id}": {"patch": {"operationId": "GuidanceController_updateReserveVisit", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceUpdateReserveVisitRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GuidanceQueryReserveVisitResponseDto"}}}}, "404": {"description": "用户不存在"}}, "tags": ["导诊工作面板"]}}, "/user/crc/register/verification-code": {"post": {"operationId": "CRCRegisterController_getVerificationCode", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CRCRegisterSendVerificationCodeRequestDto"}}}}, "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "summary": "获取验证码", "tags": ["CRCRegister"]}}, "/user/crc/register": {"post": {"operationId": "CRCRegisterController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CRCRegisterRequestDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "注册", "tags": ["CRCRegister"]}}, "/ddd-research-projects": {"post": {"operationId": "DDDResearchProjectsController_createResearchProject", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectRequestDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectResponseDto"}}}}, "409": {"description": "\n    存在项目冲突, 检查以下信息:\n\n    - 存在相同项目代码的研究项目\n    "}}, "summary": "创建研究项目", "tags": ["研究项目列表"]}, "get": {"operationId": "DDDResearchProjectsController_getAllResearchProjects", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetAllResearchProjectResponseDto"}}}}}}, "summary": "获取所有研究项目", "tags": ["研究项目列表"]}}, "/ddd-research-projects/{id}": {"get": {"operationId": "DDDResearchProjectsController_getProjectByID", "parameters": [{"name": "id", "required": true, "in": "path", "description": "项目 ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetProjectDetailsResponseDto"}}}}, "404": {"description": "项目不存在"}}, "summary": "获取对应项目", "tags": ["研究项目列表"]}, "patch": {"operationId": "DDDResearchProjectsController_updateResearchProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateResearchProjectRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateResearchProjectResponseDto"}}}}}, "summary": "更新研究项目", "tags": ["研究项目列表"]}}, "/ddd-research-projects/{projectID}/members": {"post": {"operationId": "DDDResearchProjectsController_addProjectMember", "parameters": [{"name": "projectID", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddMemberRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddMemberResponseDto"}}}}}, "summary": "添加项目成员", "tags": ["研究项目列表"]}, "put": {"operationId": "DDDResearchProjectsController_updateProjectMember", "parameters": [{"name": "projectID", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectMemberRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectMemberResponseDto"}}}}}, "summary": "更新项目成员", "tags": ["研究项目列表"]}, "get": {"operationId": "DDDResearchProjectsController_getProjectMembers", "parameters": [{"name": "projectID", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "获取项目的成员表", "tags": ["研究项目列表"]}}, "/ddd-research-projects/{projectID}/members/{memberID}": {"delete": {"operationId": "DDDResearchProjectsController_removeProjectMember", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "移除项目成员", "tags": ["研究项目列表"]}}, "/ddd-research-projects-temp-user": {"get": {"operationId": "DDDResearchProjectsTempUserController_getUser", "parameters": [{"name": "username", "required": true, "in": "query", "description": "用户名", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TempGetUserQueryResponseDto"}}}}}}, "summary": "获取项目成员用户列表", "tags": ["项目成员用户列表"]}}, "/referral-management/referrer": {"post": {"operationId": "ReferralManagementController_addReferral", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReferrerRequestDto"}}}}, "responses": {"201": {"description": "推荐人添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReferrerResponseDto"}}}}, "409": {"description": "推荐人手机号已存在"}}, "summary": "添加推荐人", "tags": ["推荐联系人管理"]}, "get": {"operationId": "ReferralManagementController_getReferrers", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "请求数量限制", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "after", "required": false, "in": "query", "description": "下一页数据的游标", "schema": {"nullable": true, "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx or 12345", "type": "string"}}, {"name": "before", "required": false, "in": "query", "description": "上一页数据的游标", "schema": {"nullable": true, "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx or 12345", "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序字段", "schema": {"default": "createdAt", "example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "排序顺序", "schema": {"default": "desc", "example": "desc", "type": "string"}}, {"name": "referrerName", "required": false, "in": "query", "description": "按推荐人姓名模糊搜索", "schema": {"example": "李", "type": "string"}}, {"name": "referrerPhone", "required": false, "in": "query", "description": "按推荐人手机号精确或模糊搜索", "schema": {"example": "139", "type": "string"}}], "responses": {"200": {"description": "推荐人查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CursorPaginatedGetReferrersResponseDto"}}}}}, "summary": "分页查询推荐人", "tags": ["推荐联系人管理"]}}, "/referral-management/referrer/{id}": {"put": {"operationId": "ReferralManagementController_updateReferral", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReferrerRequestDto"}}}}, "responses": {"200": {"description": "推荐人更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReferrerResponseDto"}}}}, "404": {"description": "推荐人不存在"}, "409": {"description": "推荐人手机号已存在"}}, "summary": "更新推荐人", "tags": ["推荐联系人管理"]}, "get": {"operationId": "ReferralManagementController_getReferrerById", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "推荐人查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetReferrersResponseDto"}}}}}, "summary": "查询推荐人", "tags": ["推荐联系人管理"]}}, "/referral-management/association": {"post": {"operationId": "ReferralManagementController_createAssociation", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAssociationRecordDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "创建推荐人与患者的关联", "tags": ["推荐联系人管理"]}, "get": {"operationId": "ReferralManagementController_getAssociationRecords", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "请求数量限制", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "after", "required": false, "in": "query", "description": "下一页数据的游标", "schema": {"nullable": true, "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx or 12345", "type": "string"}}, {"name": "before", "required": false, "in": "query", "description": "上一页数据的游标", "schema": {"nullable": true, "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx or 12345", "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序字段", "schema": {"default": "createdAt", "example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "排序顺序", "schema": {"default": "desc", "example": "desc", "type": "string"}}, {"name": "patientName", "required": false, "in": "query", "description": "按患者姓名模糊搜索", "schema": {"example": "李", "type": "string"}}, {"name": "patientPhone", "required": false, "in": "query", "description": "按患者手机号精确或模糊搜索", "schema": {"example": "139", "type": "string"}}, {"name": "referrerName", "required": false, "in": "query", "description": "按推荐人姓名模糊搜索", "schema": {"example": "李", "type": "string"}}, {"name": "referrerPhone", "required": false, "in": "query", "description": "按推荐人手机号精确或模糊搜索", "schema": {"example": "139", "type": "string"}}], "responses": {"200": {"description": "推荐人与患者的关联记录查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CursorPaginatedGetAssociationRecordResponseDto"}}}}}, "summary": "分页查询推荐人与患者的关联记录(基于游标)", "tags": ["推荐联系人管理"]}}, "/referral-management/association/{id}": {"put": {"operationId": "ReferralManagementController_updateAssociation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关联记录ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAssociationRecordDto"}}}}, "responses": {"200": {"description": "推荐人与患者的关联记录更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAssociationRecordResponseDto"}}}}, "404": {"description": "关联记录不存在"}}, "summary": "更新推荐人与患者的关联", "tags": ["推荐联系人管理"]}, "get": {"operationId": "ReferralManagementController_getAssociationRecordById", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "推荐人与患者的关联记录查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAssociationRecordResponseDto"}}}}}, "summary": "查询推荐人与患者的关联记录", "tags": ["推荐联系人管理"]}}, "/referral-management/no-referrer-mbgl-patients": {"get": {"operationId": "ReferralManagementController_getNoReferrerMbglPatients", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "请求数量限制", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "after", "required": false, "in": "query", "description": "慢病管理患者id,下一页数据的游标", "schema": {"nullable": true, "example": "9000", "type": "string"}}, {"name": "before", "required": false, "in": "query", "description": "慢病管理患者id,上一页数据的游标", "schema": {"nullable": true, "example": "9000", "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序字段", "schema": {"default": "user_id", "example": "user_id", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "排序顺序", "schema": {"default": "desc", "example": "desc", "type": "string"}}, {"name": "patientName", "required": false, "in": "query", "description": "按患者姓名模糊搜索", "schema": {"example": "李", "type": "string"}}, {"name": "patientPhone", "required": false, "in": "query", "description": "按患者手机号精确或模糊搜索", "schema": {"example": "139", "type": "string"}}], "responses": {"200": {"description": "没有推荐人的慢病管理患者查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CursorPaginatedGetNoReferrerMbglPatientsResponseDto"}}}}}, "summary": "查询没有推荐人的慢病管理患者", "tags": ["推荐联系人管理"]}}}, "info": {"title": "GCPM-BACKEND", "description": "GCPM-BACKEND API 描述", "version": "0.1", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateVisitReserveRequestDto": {"type": "object", "properties": {"serialNumber": {"type": "number", "description": "预约ID"}, "visit_date": {"format": "date-time", "type": "string", "description": "预约日期"}, "user_id": {"type": "string", "description": "用户ID"}, "user_name": {"type": "string", "description": "用户名"}, "phone": {"type": "string", "description": "电话号码"}, "project_serial_number": {"type": "number", "description": "金数据中的项目序号"}, "project_id": {"type": "string", "description": "项目ID", "nullable": true}, "project_shortname": {"type": "string", "description": "项目简称", "nullable": true}, "crcname": {"type": "string", "description": "CRC名称", "nullable": true}, "visit_name": {"type": "string", "description": "访视名称", "nullable": true}, "visit_type": {"type": "string", "description": "访视类型", "nullable": true}, "lungfunctionovertime_list": {"type": "string", "description": "肺功能加班列表", "nullable": true}, "dispense_drug_needs": {"type": "number", "description": "是否需要发药", "nullable": true}, "drug_saving_type": {"type": "string", "description": "药物保存类型", "nullable": true}, "drug_saving_location": {"type": "string", "description": "药物保存位置", "nullable": true}, "goldata_created_at": {"format": "date-time", "type": "string", "description": "创建时间"}, "goldata_updated_at": {"format": "date-time", "type": "string", "description": "更新时间"}, "info_platform": {"type": "string", "description": "信息平台", "nullable": true}, "info_filling_duration": {"type": "number", "description": "信息填写时长", "nullable": true}, "patient_fasting_needs": {"type": "number", "description": "患者是否需要空腹"}, "patient_blood_test_needs": {"type": "number", "description": "患者是否需要采血"}, "nurse_overtime_for_blood_test": {"description": "护士采血加班", "nullable": true, "type": "array", "items": {"type": "string"}}, "special_examination": {"description": "需要特殊检查请勾选", "nullable": true, "type": "array", "items": {"type": "string"}}}, "required": ["serialNumber", "visit_date", "user_id", "user_name", "phone", "project_serial_number", "project_id", "project_shortname", "crcname", "visit_name", "visit_type", "lungfunctionovertime_list", "dispense_drug_needs", "drug_saving_type", "drug_saving_location", "goldata_created_at", "goldata_updated_at", "info_platform", "info_filling_duration", "patient_fasting_needs", "patient_blood_test_needs", "nurse_overtime_for_blood_test", "special_examination"]}, "CreateVisitCompleteRecordRequestDto": {"type": "object", "properties": {}}, "FinishVisitCreateEvents": {"type": "object", "properties": {"知情": {"type": "object", "properties": {"日期": {"type": "string"}, "知情医生": {"type": "string"}, "筛选号": {"type": "string"}}, "required": []}, "随机": {"type": "object", "properties": {"日期": {"type": "string"}, "随机结果": {"type": "string"}}, "required": []}, "筛选": {"type": "object", "properties": {"日期": {"type": "string"}, "筛选结果": {"type": "string"}, "失败原因": {"type": "string", "nullable": true}}, "required": []}, "出组": {"type": "object", "properties": {"日期": {"type": "string"}, "出组": {"type": "string"}}, "required": []}}}, "ClinicalDataVisitReserveEntity": {"type": "object", "properties": {}}, "FinishVisitCreateDto": {"type": "object", "properties": {"基础访视": {"type": "object", "properties": {"visitReserve": {"type": "object", "allOf": [{"$ref": "#/components/schemas/ClinicalDataVisitReserveEntity"}], "additionalProperties": false}, "备注": {"type": "string"}, "访视名称": {"type": "string"}, "childEvents": {"type": "object", "allOf": [{"$ref": "#/components/schemas/FinishVisitCreateEvents"}], "additionalProperties": false}}}}, "required": ["基础访视"]}, "VisitCompleteRecords": {"type": "object", "properties": {"id": {"type": "number"}, "user_id": {"type": "number"}, "created_at": {"format": "date-time", "type": "string"}, "updated_at": {"format": "date-time", "type": "string"}, "reserve_id": {"type": "number"}, "record_infos": {"type": "object"}}, "required": ["id", "user_id", "created_at", "updated_at", "reserve_id", "record_infos"]}, "VisitReserveResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "预约ID"}, "visit_date": {"type": "string", "description": "访视日期", "format": "date"}, "user_id": {"type": "string", "description": "用户ID"}, "user_name": {"type": "string", "description": "用户姓名"}, "phone": {"type": "string", "description": "电话号码"}, "project_id": {"type": "string", "description": "项目ID"}, "project_shortname": {"type": "string", "description": "项目简称"}, "crcname": {"type": "string", "description": "CRC姓名"}, "visit_name": {"type": "string", "description": "访视名称"}, "visit_type": {"type": "string", "description": "访视类型"}, "lungfunctionovertime_list": {"description": "肺功能加班列表", "nullable": true, "type": "array", "items": {"type": "string"}}, "patient_fasting_needs": {"type": "number", "description": "患者是否需要空腹", "enum": [0, 1]}, "patient_blood_test_needs": {"type": "number", "description": "患者是否需要采血", "enum": [0, 1]}, "needECG": {"type": "number", "description": "是否需要心电图", "enum": [0, 1]}, "needCentrifuge": {"type": "number", "description": "是否需要离心机", "enum": [0, 1]}, "needFreezer": {"type": "number", "description": "是否需要标本冰箱", "enum": [0, 1]}, "nurse_overtime_for_blood_test": {"description": "护士采血加班", "nullable": true, "type": "array", "items": {"type": "string"}}, "special_examination": {"description": "特殊检查", "nullable": true, "type": "array", "items": {"type": "string"}}, "dispense_drug_needs": {"type": "number", "description": "是否需要发放药物", "enum": [0, 1]}, "drug_saving_type": {"type": "string", "description": "药物保存类型"}, "drug_saving_location": {"type": "string", "description": "药物保存位置"}, "encrypted_reserve_infos": {"type": "string", "description": "加密的预约信息（十六进制字符串）", "format": "hex"}, "goldata_created_at": {"format": "date-time", "type": "string", "description": "Goldata创建时间"}, "goldata_updated_at": {"format": "date-time", "type": "string", "description": "Goldata更新时间"}, "info_platform": {"type": "string", "description": "信息平台"}, "info_filling_duration": {"type": "number", "description": "填写持续时间（秒）"}, "finished_status": {"type": "number", "description": "完成状态", "enum": [0, 1]}, "created_at": {"format": "date-time", "type": "string", "description": "创建时间"}, "updated_at": {"format": "date-time", "type": "string", "description": "更新时间"}, "project_serial_number": {"type": "number", "description": "项目序列号"}, "visitTypeID": {"type": "number", "description": "访视类型ID"}, "mbglProjectID": {"type": "number", "description": "MBGL项目ID"}, "serialNumber": {"type": "number", "description": "序列号"}, "mbglUserID": {"type": "number", "description": "MBGL用户ID"}, "need_center_visit_assistance_info": {"description": "中心提供访视协办事务补充信息", "nullable": true, "type": "array", "items": {"type": "string"}}, "remark": {"type": "string", "description": "备注", "nullable": true}, "cancelReason": {"type": "string", "description": "取消原因", "nullable": true}, "createBy": {"type": "string", "description": "创建者"}, "patient_stay_in_center": {"type": "number", "description": "患者是否在中心留宿"}, "mainVisitContent": {"type": "string", "description": "主要访视内容", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}}, "required": ["id", "visit_date", "user_id", "user_name", "phone", "lungfunctionovertime_list", "patient_fasting_needs", "patient_blood_test_needs", "needECG", "needCentrifuge", "<PERSON><PERSON><PERSON><PERSON>", "nurse_overtime_for_blood_test", "special_examination", "goldata_created_at", "goldata_updated_at", "finished_status", "created_at", "updated_at", "project_serial_number", "visitTypeID", "mbglProjectID", "serialNumber", "mbglUserID", "need_center_visit_assistance_info", "remark", "cancelReason", "createBy", "patient_stay_in_center", "mainVisitContent", "isUnplannedVisit"]}, "UpdateVisitDto": {"type": "object", "properties": {}}, "DepartmentStaff": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}}, "required": ["name", "phone"]}, "IsVisitCompleteDto": {"type": "object", "properties": {"isVisitComplete": {"type": "boolean"}}, "required": ["isVisitComplete"]}, "ClinicalDataVisitReserveCreateRequestDto": {"type": "object", "properties": {"visitTypeID": {"type": "number", "description": "访视类型ID"}, "visitDate": {"type": "string", "description": "访视预约日期"}, "mbglUserID": {"type": "number", "description": "访视预约患者ID（来自慢病管理系统）"}, "mbglProjectID": {"type": "number", "description": "访视预约项目 ID（来自慢病管理系统）"}, "crcName": {"type": "string", "description": "访视预约 CRC 名称"}}, "required": ["visitTypeID", "visitDate", "mbglUserID", "mbglProjectID", "crcName"]}, "FinishVisitMbglCheckDto": {"type": "object", "properties": {"sysCaseTempleNames": {"type": "array", "items": {"type": "string"}}, "mbglUserId": {"type": "number"}, "jsjProjectId": {"type": "number", "nullable": true}, "mbglProjectId": {"type": "number", "nullable": true}}, "required": ["sysCaseTempleNames", "mbglUserId", "jsjProjectId", "mbglProjectId"]}, "ReserveVisitPatientStateDto": {"type": "object", "properties": {"patientId": {"type": "number", "description": "患者ID"}, "mbglProjectId": {"type": "number", "description": "慢病管理系统项目ID"}, "jsjProjectId": {"type": "number", "description": "金数据项目序号"}}, "required": ["patientId", "mbglProjectId", "jsjProjectId"]}, "UpdateVisitReserveDto": {"type": "object", "properties": {"mbglProjectID": {"type": "number", "description": "项目ID"}, "mbglUserID": {"type": "number", "description": "患者ID"}, "visitTypeID": {"type": "number", "description": "访视类型ID", "nullable": true}, "visitDate": {"type": "string", "description": "访视预约的日期"}, "crcName": {"type": "string", "description": "CRC 姓名"}}, "required": ["mbglProjectID", "mbglUserID", "visitTypeID", "visitDate", "crcName"]}, "ReserveCheckMBGLSyncResultDto": {"type": "object", "properties": {"result": {"type": "string", "enum": ["not_synced", "synced", "failed"]}}, "required": ["result"]}, "CrcCreateReserveVisitDto": {"type": "object", "properties": {"mbglUserID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "visitTypeID": {"type": "string", "description": "访视类型 Id"}, "visitTypeOtherInfo": {"type": "string", "description": "访视类型补充信息", "nullable": true}, "visitDate": {"type": "string", "description": "访视日期"}, "visitName": {"type": "string", "description": "访视名称"}, "lungfunctionovertimeList": {"description": "是否需要肺功能加班", "default": [], "type": "array", "items": {"type": "string"}}, "patientFastingNeeds": {"type": "string", "description": "患者是否需要空腹"}, "patientBloodTestNeeds": {"type": "string", "description": "患者是否需要采血"}, "patientStayInCenter": {"type": "string", "description": "患者是否在中心留宿"}, "nurseOvertimeForBloodTest": {"description": "护士采血加班", "default": [], "type": "array", "items": {"type": "string"}}, "specialExamination": {"description": "特殊检查", "default": [], "type": "array", "items": {"type": "string"}}, "dispenseDrugNeeds": {"type": "string", "description": "是否需要发放药物"}, "drugSavingLocation": {"type": "string", "description": "药物保存位置", "nullable": true}, "needCenterVisitAssistance": {"description": "需要中心提供访视协办事务", "default": [], "type": "array", "items": {"type": "string"}}, "remark": {"type": "string", "description": "备注", "nullable": true}, "specialExaminationOtherInfo": {"type": "string", "description": "特殊检查补充信息", "nullable": true}, "drugSavingLocationOtherInfo": {"type": "string", "description": "中心提供访视协办事务补充信息", "nullable": true}, "createBy": {"type": "string", "description": "创建者"}, "mainVisitContent": {"description": "主要访视内容", "default": [], "type": "array", "items": {"type": "string"}}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}}, "required": ["mbglUserID", "mbglProjectID", "visitTypeID", "visitTypeOtherInfo", "visitDate", "visitName", "lungfunctionovertimeList", "patientFastingNeeds", "patientBloodTestNeeds", "patientStayInCenter", "nurseOvertimeForBloodTest", "specialExamination", "dispenseDrugNeeds", "drugSavingLocation", "needCenterVisitAssistance", "remark", "specialExaminationOtherInfo", "drugSavingLocationOtherInfo", "createBy", "mainVisitContent", "isUnplannedVisit"]}, "PatientCreateRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}, "birthday": {"type": "string", "description": "患者生日"}, "address": {"type": "string", "description": "患者地址"}, "idNumber": {"type": "string", "description": "患者身份证号", "nullable": true}, "diseases": {"description": "患者疾病", "type": "array", "items": {"type": "string"}}}, "required": ["name", "phone", "gender", "birthday", "address", "diseases"]}, "PatientCreateResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "患者ID"}, "name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}, "birthday": {"format": "date-time", "type": "string", "description": "患者生日"}, "address": {"type": "string", "description": "患者地址"}, "idNumber": {"type": "string", "description": "患者身份证号"}, "mbglUserID": {"type": "string", "description": "患者在慢病管理系统中的 ID", "nullable": true}}, "required": ["id", "name", "phone", "gender", "birthday", "address", "idNumber"]}, "PatientQueryResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "患者ID"}, "name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}, "birthday": {"type": "string", "description": "患者生日"}, "address": {"type": "string", "description": "患者地址", "nullable": true}, "idNumber": {"type": "string", "description": "患者身份证号", "nullable": true}, "currentResearchStatusID": {"type": "number", "description": "当前研究状态ID"}, "isActive": {"type": "boolean", "description": "是否激活"}, "mbglUserID": {"type": "number", "description": "慢病管理系统患者ID", "nullable": true}, "diseases": {"description": "疾病列表", "type": "array", "items": {"type": "number"}}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"format": "date-time", "type": "string", "nullable": true}}, "required": ["id", "name", "phone", "gender", "birthday", "address", "idNumber", "currentResearchStatusID", "isActive", "mbglUserID", "diseases", "createdAt", "updatedAt", "deleted", "deletedAt"]}, "MbglPatientCreateRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}, "birthday": {"type": "string", "description": "患者生日"}}, "required": ["name", "phone", "gender", "birthday"]}, "PatientPaginationResponseDto": {"type": "object", "properties": {"data": {"description": "患者列表", "type": "array", "items": {"$ref": "#/components/schemas/PatientQueryResponseDto"}}, "nextCursor": {"type": "string", "description": "下一页游标", "nullable": true}}, "required": ["data", "nextCursor"]}, "PatientUpdateRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}, "birthday": {"type": "string", "description": "患者生日"}, "address": {"type": "string", "description": "患者地址", "nullable": true}, "idNumber": {"type": "string", "description": "患者身份证号", "nullable": true}, "diseases": {"description": "患者疾病", "type": "array", "items": {"type": "number"}}}, "required": ["name", "phone", "gender", "birthday", "address", "idNumber", "diseases"]}, "PatientUpdateResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "患者ID"}, "name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}, "birthday": {"type": "string", "description": "患者生日"}, "address": {"type": "string", "description": "患者地址"}, "idNumber": {"type": "string", "description": "患者身份证号"}, "currentResearchStatusID": {"type": "number", "description": "当前研究状态ID"}, "isActive": {"type": "boolean", "description": "是否激活"}, "mbglUserID": {"type": "number", "description": "慢病管理系统患者ID", "nullable": true}}, "required": ["id", "name", "phone", "gender", "birthday", "address", "idNumber", "currentResearchStatusID", "isActive", "mbglUserID"]}, "MbglPatientQueryResponseDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "患者ID"}, "userName": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "gender": {"type": "number", "description": "患者性别"}}, "required": ["userId", "userName", "phone", "gender"]}, "MbglDiseaseQueryResponseDto": {"type": "object", "properties": {"name": {"type": "string", "description": "疾病名称"}, "id": {"type": "number", "description": "疾病ID"}}, "required": ["name", "id"]}, "MbglSysRecommendPatientResponseDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "患者ID"}, "userName": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}, "diseases": {"description": "疾病", "type": "array", "items": {"$ref": "#/components/schemas/MbglDiseaseQueryResponseDto"}}, "gender": {"type": "string", "description": "患者性别"}, "age": {"type": "number", "description": "患者年龄"}}, "required": ["userId", "userName", "phone", "diseases", "gender", "age"]}, "MbglProjectQueryResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "项目ID"}, "name": {"type": "string", "description": "项目名称"}}, "required": ["id", "name"]}, "CreateMbglSystemUsersDto": {"type": "object", "properties": {}}, "UpdateMbglSystemUsersDto": {"type": "object", "properties": {}}, "CreateClinicalEventDto": {"type": "object", "properties": {}}, "CreateClinicalDeviceEventDto": {"type": "object", "properties": {}}, "CreateClinicalDeviceEventRequestDto": {"type": "object", "properties": {"createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}, "eventDate": {"format": "date-time", "type": "string", "description": "事件日期"}, "deviceId": {"type": "number", "description": "设备ID"}, "managementEventType": {"type": "string", "description": "管理事件类型"}, "minTemperature": {"type": "number", "description": "最小温度"}, "maxTemperature": {"type": "number", "description": "最大温度"}, "clinicalProjectId": {"type": "string", "description": "临床项目ID"}, "crcName": {"type": "string", "description": "CRC名称"}, "drugExpirationDate": {"format": "date-time", "type": "string", "description": "药物过期日期"}, "createBy": {"type": "string", "description": "创建人"}}, "required": ["createdAt", "updatedAt", "eventDate", "deviceId", "managementEventType", "minTemperature", "maxTemperature", "clinicalProjectId", "crcName", "drugExpirationDate", "createBy"]}, "CreateClinicalDeviceBindingRequestDto": {"type": "object", "properties": {"clinicalProjectId": {"type": "string", "description": "临床项目ID"}, "deviceId": {"type": "number", "description": "设备ID"}, "createBy": {"type": "string", "description": "创建人"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["clinicalProjectId", "deviceId", "createBy", "createdAt", "updatedAt"]}, "CreateClinicalDeviceRequestDto": {"type": "object", "properties": {"serialNumber": {"type": "object", "description": "金数据序号"}, "id": {"type": "number", "description": "临床设备ID"}, "deviceType": {"type": "string", "description": "设备类型"}, "refrigeratorType": {"type": "string", "description": "冰箱类型"}, "provider": {"type": "string", "description": "设备提供方"}, "deviceLocation": {"type": "string", "description": "设备放置位置"}, "deviceLabel": {"type": "string", "description": "设备标签"}, "createBy": {"type": "string", "description": "创建人"}}, "required": ["serialNumber", "id", "deviceType", "refrigeratorType", "provider", "deviceLocation"]}, "CreateProjectFilingDto": {"type": "object", "properties": {}}, "ResearchProjectEditBasicInfoDto": {"type": "object", "properties": {}}, "Subject": {"type": "object", "properties": {"researchProjectUUID": {"type": "string", "description": "项目UUID"}, "id": {"type": "number", "description": "受试者id"}, "patientUUID": {"type": "string", "description": "受试者uuid"}, "tempMBGLUserID": {"type": "string", "description": "慢病管理系统用户id"}, "tempJSJProjectID": {"type": "number", "description": "金数据序号"}, "tempMBGLProjectID": {"type": "number", "description": "慢病管理系统项目id"}, "researchProjectID": {"type": "number", "description": "研究项目id"}, "stateID": {"type": "number", "description": "受试者状态id"}, "patientID": {"type": "number", "description": "患者id"}, "screeningNumber": {"type": "string", "description": "受试号（筛选号）"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["researchProjectUUID", "id", "patientUUID", "tempMBGLUserID", "tempJSJProjectID", "tempMBGLProjectID", "researchProjectID", "stateID", "patientID", "screeningNumber", "createdAt", "updatedAt"]}, "SubjectEvent": {"type": "object", "properties": {"id": {"type": "number", "description": "事件id"}, "subjectID": {"type": "number", "description": "受试者id"}, "eventTypeID": {"type": "number", "description": "事件类型id"}, "eventTime": {"format": "date-time", "type": "string", "description": "事件时间"}, "content": {"type": "string", "description": "事件内容"}, "remark": {"type": "string", "description": "备注"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "parentEventID": {"type": "number", "description": "父事件id"}}, "required": ["id", "subjectID", "eventTypeID", "eventTime", "content", "remark", "createdAt", "updatedAt", "parentEventID"]}, "ProjectQueryResponseDto": {"type": "object", "properties": {"name": {"type": "string", "description": "项目名称"}, "id": {"type": "number", "description": "项目ID"}}, "required": ["name", "id"]}, "SubjectEventQueryDto": {"type": "object", "properties": {"id": {"type": "number", "description": "事件id"}, "subjectID": {"type": "number", "description": "受试者id"}, "eventTypeID": {"type": "number", "description": "事件类型id"}, "eventTime": {"format": "date-time", "type": "string", "description": "事件时间"}, "remark": {"type": "string", "description": "备注"}, "parentEventID": {"type": "number", "description": "父事件id"}, "content": {"type": "object", "description": "事件内容"}}, "required": ["id", "subjectID", "eventTypeID", "eventTime", "remark", "parentEventID", "content"]}, "UpdateTeamMemberInfoDto": {"type": "object", "properties": {}}, "AddTeamMemberDto": {"type": "object", "properties": {}}, "LoginRequestDto": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}}, "required": ["username", "password"]}, "LoginResponseDto": {"type": "object", "properties": {}}, "RegisterRequestDto": {"type": "object", "properties": {}}, "RegisterResponseDto": {"type": "object", "properties": {}}, "GetUserProfileResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户资料ID"}, "firstName": {"type": "string", "description": "姓"}, "lastName": {"type": "string", "description": "名"}, "phone": {"type": "string", "description": "电话号码", "nullable": true}}, "required": ["id", "firstName", "lastName", "phone"]}, "GetUserDetailResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户ID"}, "name": {"type": "string", "description": "用户名称"}, "phone": {"type": "string", "description": "用户手机号"}, "email": {"type": "string", "description": "用户邮箱"}, "userTypeId": {"type": "string", "description": "用户类型ID"}, "displayName": {"type": "string", "description": "用户显示名称"}, "userProfile": {"description": "用户资料", "nullable": true, "allOf": [{"$ref": "#/components/schemas/GetUserProfileResponseDto"}]}}, "required": ["id", "name", "phone", "email", "userTypeId", "displayName", "userProfile"]}, "GetCrcWhitelistResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID"}, "name": {"type": "string", "description": "姓名"}, "phone": {"type": "string", "description": "电话号码"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["id", "name", "phone", "email"]}, "CreateCRCWhitelistRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "姓名"}, "phone": {"type": "string", "description": "电话号码"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["name", "phone", "email"]}, "UpdateCRCWhitelistRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "姓名"}, "phone": {"type": "string", "description": "电话号码"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["name", "phone", "email"]}, "SubjectState": {"type": "object", "properties": {"id": {"type": "number", "description": "受试者状态id"}, "code": {"type": "string", "description": "受试者状态代码"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "code", "createdAt", "updatedAt"]}, "SubjectEventTypeResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "事件类型id"}, "code": {"type": "string", "description": "事件类型代码"}, "priority": {"type": "number", "description": "事件优先级"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "code", "priority", "createdAt", "updatedAt"]}, "TempJSJVisitTypeResponseDto": {"type": "object", "properties": {"id": {"type": "number"}, "visitTypeCode": {"type": "string"}}, "required": ["id", "visitTypeCode"]}, "GetDiseaseResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "疾病ID"}, "name": {"type": "string", "description": "疾病名称"}}, "required": ["id", "name"]}, "GetProjectPhaseResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "项目阶段ID"}, "name": {"type": "string", "description": "项目阶段名称"}}, "required": ["id", "name"]}, "GetProjectStatusResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "项目状态ID"}, "name": {"type": "string", "description": "项目状态名称"}}, "required": ["id", "name"]}, "GetSponsorResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "申办方ID"}, "name": {"type": "string", "description": "申办方名称"}}, "required": ["id", "name"]}, "GetObjectTypeResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "研究对象类型ID"}, "name": {"type": "string", "description": "研究对象类型名称"}}, "required": ["id", "name"]}, "GetProjectRoleTypeResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "项目角色类型ID"}, "name": {"type": "string", "description": "项目角色类型名称"}}, "required": ["id", "name"]}, "CreateGoldatumDto": {"type": "object", "properties": {}}, "UpdateGoldatumDto": {"type": "object", "properties": {}}, "CreateDocumentRequestDto": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "documentType": {"type": "string", "description": "文档类型", "enum": ["肺功能报告", "知情同意书", "项目方案"]}, "createBy": {"type": "string", "description": "创建者"}}, "required": ["files", "documentType", "createBy"]}, "CreateReserveVisitRequestDto": {"type": "object", "properties": {"mbglUserID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "visitDate": {"type": "string", "description": "访视日期"}, "visitName": {"type": "string", "description": "访视名称"}, "lungfunctionovertimeList": {"description": "是否需要肺功能加班", "default": [], "type": "array", "items": {"type": "string"}}, "patientFastingNeeds": {"type": "string", "description": "患者是否需要空腹"}, "patientBloodTestNeeds": {"type": "string", "description": "患者是否需要采血"}, "needECG": {"type": "string", "description": "是否需要心电图"}, "needCentrifuge": {"type": "string", "description": "是否需要离心机"}, "needFreezer": {"type": "string", "description": "是否需要标本冰箱"}, "patientStayInCenter": {"type": "string", "description": "是否在中心留宿"}, "nurseOvertimeForBloodTest": {"description": "护士采血加班", "default": [], "type": "array", "items": {"type": "string"}}, "specialExamination": {"description": "特殊检查", "default": [], "type": "array", "items": {"type": "string"}}, "dispenseDrugNeeds": {"type": "string", "description": "是否需要发放药物"}, "drugSavingLocation": {"type": "string", "description": "药物保存位置", "nullable": true}, "needCenterVisitAssistance": {"description": "需要中心提供访视协办事务", "default": [], "type": "array", "items": {"type": "string"}}, "remark": {"type": "string", "description": "备注", "nullable": true}, "specialExaminationOtherInfo": {"type": "string", "description": "特殊检查补充信息", "nullable": true}, "needCenterVisitAssistanceOtherInfo": {"type": "string", "description": "中心提供访视协办事务补充信息", "nullable": true}, "drugSavingLocationOtherInfo": {"type": "string", "description": "药物保存位置补充信息", "nullable": true}, "mainVisitContent": {"type": "array", "description": "主要访视内容", "default": [], "items": {"type": "string", "enum": ["知情", "随机", "出组"]}}, "mainVisitContentOtherInfo": {"type": "string", "description": "主要访视内容补充信息", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}}, "required": ["mbglUserID", "mbglProjectID", "visitDate", "visitName", "lungfunctionovertimeList", "patientFastingNeeds", "patientBloodTestNeeds", "needECG", "needCentrifuge", "<PERSON><PERSON><PERSON><PERSON>", "patientStayInCenter", "nurseOvertimeForBloodTest", "specialExamination", "dispenseDrugNeeds", "drugSavingLocation", "needCenterVisitAssistance", "remark", "specialExaminationOtherInfo", "needCenterVisitAssistanceOtherInfo", "drugSavingLocationOtherInfo", "mainVisitContent", "mainVisitContentOtherInfo", "isUnplannedVisit"]}, "UpdateReserveVisitRequestDto": {"type": "object", "properties": {"mbglUserID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "visitDate": {"type": "string", "description": "访视日期"}, "visitName": {"type": "string", "description": "访视名称"}, "lungfunctionovertimeList": {"description": "是否需要肺功能加班", "default": [], "type": "array", "items": {"type": "string"}}, "patientFastingNeeds": {"type": "string", "description": "患者是否需要空腹"}, "patientBloodTestNeeds": {"type": "string", "description": "患者是否需要采血"}, "needECG": {"type": "string", "description": "是否需要心电图"}, "needCentrifuge": {"type": "string", "description": "是否需要离心机"}, "needFreezer": {"type": "string", "description": "是否需要标本冰箱"}, "patientStayInCenter": {"type": "string", "description": "是否在中心留宿"}, "nurseOvertimeForBloodTest": {"description": "护士采血加班", "default": [], "type": "array", "items": {"type": "string"}}, "specialExamination": {"description": "特殊检查", "default": [], "type": "array", "items": {"type": "string"}}, "dispenseDrugNeeds": {"type": "string", "description": "是否需要发放药物"}, "drugSavingLocation": {"type": "string", "description": "药物保存位置", "nullable": true}, "needCenterVisitAssistance": {"description": "需要中心提供访视协办事务", "default": [], "type": "array", "items": {"type": "string"}}, "remark": {"type": "string", "description": "备注", "nullable": true}, "specialExaminationOtherInfo": {"type": "string", "description": "特殊检查补充信息", "nullable": true}, "needCenterVisitAssistanceOtherInfo": {"type": "string", "description": "中心提供访视协办事务补充信息", "nullable": true}, "drugSavingLocationOtherInfo": {"type": "string", "description": "药物保存位置补充信息", "nullable": true}, "mainVisitContent": {"type": "array", "description": "主要访视内容", "default": [], "items": {"type": "string", "enum": ["知情", "随机", "出组"]}}, "mainVisitContentOtherInfo": {"type": "string", "description": "主要访视内容补充信息", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}}, "required": ["mbglUserID", "mbglProjectID", "visitDate", "visitName", "lungfunctionovertimeList", "patientFastingNeeds", "patientBloodTestNeeds", "needECG", "needCentrifuge", "<PERSON><PERSON><PERSON><PERSON>", "patientStayInCenter", "nurseOvertimeForBloodTest", "specialExamination", "dispenseDrugNeeds", "drugSavingLocation", "needCenterVisitAssistance", "remark", "specialExaminationOtherInfo", "needCenterVisitAssistanceOtherInfo", "drugSavingLocationOtherInfo", "mainVisitContent", "mainVisitContentOtherInfo", "isUnplannedVisit"]}, "CancelReserveVisitDto": {"type": "object", "properties": {}}, "CrcReserveVisitResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "访视预约 Id"}, "mbglProjectId": {"type": "number", "description": "慢病管理系统中的项目信息"}, "mbglPatientId": {"type": "number", "description": "慢病管理系统中的患者 Id"}, "visitDate": {"type": "string", "description": "访视日期"}, "visitName": {"type": "string", "description": "访视名称"}, "lungfunctionovertimeList": {"description": "肺功能加班", "type": "array", "items": {"type": "string"}}, "dispenseDrugNeeds": {"type": "boolean", "description": "是否需要药物"}, "drugSavingLocation": {"type": "string", "description": "药物保存位置"}, "drugSavingLocationOtherInfo": {"type": "string", "description": "药物保存位置补充信息", "nullable": true}, "patientBloodTestNeeds": {"type": "boolean", "description": "是否需要采血"}, "needCentrifuge": {"type": "boolean", "description": "是否需要离心机"}, "needECG": {"type": "boolean", "description": "是否需要心电图"}, "needFreezer": {"type": "boolean", "description": "是否需要冰箱"}, "patientFastingNeeds": {"type": "boolean", "description": "是否需要空腹检查"}, "nurseOvertimeForBloodTest": {"description": "护士采血加班", "type": "array", "items": {"type": "string"}}, "patientStayInCenter": {"type": "boolean", "description": "是否在中心留宿"}, "specialExamination": {"description": "特殊检查", "type": "array", "items": {"type": "string"}}, "needCenterVisitAssistance": {"description": "中心提供访视协办事务", "type": "array", "items": {"type": "string"}}, "remark": {"type": "string", "description": "备注", "nullable": true}, "specialExaminationOtherInfo": {"type": "string", "description": "特殊检查补充信息", "nullable": true}, "needCenterVisitAssistanceOtherInfo": {"type": "string", "description": "中心提供访视协办事务补充信息", "nullable": true}, "cancelReason": {"type": "string", "description": "取消原因", "nullable": true}, "mainVisitContent": {"description": "主要访视内容", "type": "array", "items": {"type": "string"}}, "mainVisitContentOtherInfo": {"type": "string", "description": "主要访视内容补充信息", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}}, "required": ["id", "mbglProjectId", "mbglPatientId", "visitDate", "visitName", "lungfunctionovertimeList", "dispenseDrugNeeds", "drugSavingLocation", "drugSavingLocationOtherInfo", "patientBloodTestNeeds", "needCentrifuge", "needECG", "<PERSON><PERSON><PERSON><PERSON>", "patientFastingNeeds", "nurseOvertimeForBloodTest", "patientStayInCenter", "specialExamination", "needCenterVisitAssistance", "remark", "specialExaminationOtherInfo", "needCenterVisitAssistanceOtherInfo", "cancelReason", "mainVisitContent", "mainVisitContentOtherInfo", "isUnplannedVisit"]}, "MbglPatientResponseDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "gender": {"type": "string"}, "phone": {"type": "string"}}, "required": ["id", "name", "gender", "phone"]}, "MbglProjectResponseDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "ReserveVisitPaginationResponseDto": {"type": "object", "properties": {"data": {"description": "访视预约列表", "type": "array", "items": {"$ref": "#/components/schemas/CrcReserveVisitResponseDto"}}, "patients": {"description": "患者列表", "type": "array", "items": {"$ref": "#/components/schemas/MbglPatientResponseDto"}}, "projects": {"description": "项目列表", "type": "array", "items": {"$ref": "#/components/schemas/MbglProjectResponseDto"}}, "nextCursor": {"type": "string", "description": "下一页游标", "nullable": true}}, "required": ["data", "patients", "projects", "nextCursor"]}, "CreateQualityControlAEEventRequestDto": {"type": "object", "properties": {"mbglPatientID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "judgementDoctorId": {"type": "string", "description": "判定医生 Id"}, "detail": {"type": "string", "description": "事件详情"}, "date": {"type": "string", "description": "事件日期"}, "drugRelated": {"type": "string", "description": "药物相关性"}}, "required": ["mbglPatientID", "mbglProjectID", "judgementDoctorId", "detail", "date", "drugRelated"]}, "CreateQualityControlSAEEventRequestDto": {"type": "object", "properties": {"mbglPatientID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "judgementDoctorId": {"type": "string", "description": "判定医生 Id"}, "detail": {"type": "string", "description": "事件详情"}, "date": {"type": "string", "description": "事件日期"}, "drugRelated": {"type": "string", "description": "药物相关性"}}, "required": ["mbglPatientID", "mbglProjectID", "judgementDoctorId", "detail", "date", "drugRelated"]}, "CreateQualityControlPDEventRequestDto": {"type": "object", "properties": {"mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "detail": {"type": "string", "description": "事件详情"}, "date": {"type": "string", "description": "事件日期"}, "issueType": {"type": "string", "description": "问题类型"}}, "required": ["mbglProjectID", "detail", "date", "issueType"]}, "QueryQualityControlAEEventDto": {"type": "object", "properties": {"id": {"type": "string"}, "mbglProjectId": {"type": "number"}, "mbglPatientId": {"type": "number"}, "judgementDoctorId": {"type": "string"}, "date": {"format": "date-time", "type": "string"}, "detail": {"type": "string"}, "drugRelated": {"type": "string"}, "createBy": {"type": "string"}}, "required": ["id", "mbglProjectId", "mbglPatientId", "judgementDoctorId", "date", "detail", "drugRelated", "createBy"]}, "QueryQualityControlSAEEventDto": {"type": "object", "properties": {"id": {"type": "string"}, "mbglProjectId": {"type": "number"}, "mbglPatientId": {"type": "number"}, "judgementDoctorId": {"type": "string"}, "date": {"format": "date-time", "type": "string"}, "detail": {"type": "string"}, "drugRelated": {"type": "string"}, "createBy": {"type": "string"}}, "required": ["id", "mbglProjectId", "mbglPatientId", "judgementDoctorId", "date", "detail", "drugRelated", "createBy"]}, "QueryQualityControlPDEventDto": {"type": "object", "properties": {"id": {"type": "string"}, "mbglProjectId": {"type": "number"}, "date": {"format": "date-time", "type": "string"}, "detail": {"type": "string"}, "issueType": {"type": "string"}, "createBy": {"type": "string"}}, "required": ["id", "mbglProjectId", "date", "detail", "issueType", "createBy"]}, "QueryQualityControlEventDto": {"type": "object", "properties": {"aeEvents": {"type": "array", "items": {"$ref": "#/components/schemas/QueryQualityControlAEEventDto"}}, "saeEvents": {"type": "array", "items": {"$ref": "#/components/schemas/QueryQualityControlSAEEventDto"}}, "pdEvents": {"type": "array", "items": {"$ref": "#/components/schemas/QueryQualityControlPDEventDto"}}, "patients": {"type": "array", "items": {"$ref": "#/components/schemas/MbglPatientResponseDto"}}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/MbglProjectResponseDto"}}, "judgmentDoctors": {"type": "array", "items": {"$ref": "#/components/schemas/GetUserDetailResponseDto"}}}, "required": ["aeEvents", "saeEvents", "pdEvents", "patients", "projects", "judgmentDoctors"]}, "TempFinishVisitSyncResultDto": {"type": "object", "properties": {"id": {"type": "number", "description": "访视同步结果 Id"}, "reserveId": {"type": "number", "description": "访视预约 Id"}, "mbglDataId": {"type": "number", "description": "慢病管理系统中的数据 Id", "nullable": true}, "needSync": {"type": "boolean", "description": "是否需要同步"}}, "required": ["id", "reserveId", "mbglDataId", "needSync"]}, "GuidanceReserveVisitResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "访视预约 Id"}, "mbglProjectId": {"type": "number", "description": "慢病管理系统中的项目信息"}, "mbglPatientId": {"type": "number", "description": "慢病管理系统中的患者 Id"}, "visitDate": {"type": "string", "description": "访视日期"}, "visitName": {"type": "string", "description": "访视名称"}, "lungfunctionovertimeList": {"description": "肺功能加班", "type": "array", "items": {"type": "string"}}, "dispenseDrugNeeds": {"type": "boolean", "description": "是否需要药物"}, "drugSavingLocation": {"type": "string", "description": "药物保存位置"}, "drugSavingLocationOtherInfo": {"type": "string", "description": "药物保存位置补充信息", "nullable": true}, "patientBloodTestNeeds": {"type": "boolean", "description": "是否需要采血"}, "needCentrifuge": {"type": "boolean", "description": "是否需要离心机"}, "needECG": {"type": "boolean", "description": "是否需要心电图"}, "needFreezer": {"type": "boolean", "description": "是否需要冰箱"}, "patientFastingNeeds": {"type": "boolean", "description": "是否需要空腹检查"}, "patientStayInCenter": {"type": "boolean", "description": "患者是否在中心留宿"}, "nurseOvertimeForBloodTest": {"description": "护士采血加班", "type": "array", "items": {"type": "string"}}, "specialExamination": {"description": "特殊检查", "type": "array", "items": {"type": "string"}}, "needCenterVisitAssistance": {"description": "中心提供访视协办事务", "type": "array", "items": {"type": "string"}}, "remark": {"type": "string", "description": "备注", "nullable": true}, "specialExaminationOtherInfo": {"type": "string", "description": "特殊检查补充信息", "nullable": true}, "needCenterVisitAssistanceOtherInfo": {"type": "string", "description": "中心提供访视协办事务补充信息", "nullable": true}, "cancelReason": {"type": "string", "description": "取消原因", "nullable": true}, "TempFinishVisitSyncResult": {"description": "访视同步结果", "nullable": true, "allOf": [{"$ref": "#/components/schemas/TempFinishVisitSyncResultDto"}]}, "crcName": {"type": "string", "nullable": true}, "mainVisitContent": {"type": "array", "description": "主要访视内容", "default": [], "items": {"type": "string", "enum": ["知情", "随机", "出组"]}}, "mainVisitContentOtherInfo": {"type": "string", "description": "主访视内容补充信息", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为未计划访视"}}, "required": ["id", "mbglProjectId", "mbglPatientId", "visitDate", "visitName", "lungfunctionovertimeList", "dispenseDrugNeeds", "drugSavingLocation", "drugSavingLocationOtherInfo", "patientBloodTestNeeds", "needCentrifuge", "needECG", "<PERSON><PERSON><PERSON><PERSON>", "patientFastingNeeds", "patientStayInCenter", "nurseOvertimeForBloodTest", "specialExamination", "needCenterVisitAssistance", "remark", "specialExaminationOtherInfo", "needCenterVisitAssistanceOtherInfo", "cancelReason", "TempFinishVisitSyncResult", "crcName", "mainVisitContent", "mainVisitContentOtherInfo", "isUnplannedVisit"]}, "GuidanceMbglPatientResponseDto": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "age": {"type": "number", "nullable": true}, "sex": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "required": ["id", "name", "age", "sex", "phone"]}, "GuidanceQueryReserveVisitResponseDto": {"type": "object", "properties": {"reserveVisits": {"description": "访视预约列表", "type": "array", "items": {"$ref": "#/components/schemas/GuidanceReserveVisitResponseDto"}}, "patients": {"description": "患者列表", "type": "array", "items": {"$ref": "#/components/schemas/GuidanceMbglPatientResponseDto"}}, "projects": {"description": "项目列表", "type": "array", "items": {"$ref": "#/components/schemas/MbglProjectResponseDto"}}}, "required": ["reserveVisits", "patients", "projects"]}, "GuidanceCreateReserveVisitRequestDto": {"type": "object", "properties": {"mbglUserID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "visitDate": {"type": "string", "description": "访视日期"}, "mainVisitContent": {"type": "array", "description": "主要访视内容", "default": [], "items": {"type": "string", "enum": ["知情", "随机", "出组"]}}, "mainVisitContentOtherInfo": {"type": "string", "description": "主要访视内容补充信息", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}, "crcName": {"type": "string", "description": "CRC 名称", "nullable": true}}, "required": ["mbglUserID", "mbglProjectID", "visitDate", "mainVisitContent", "mainVisitContentOtherInfo", "isUnplannedVisit", "crcName"]}, "GuidanceUpdateReserveVisitRequestDto": {"type": "object", "properties": {"mbglUserID": {"type": "string", "description": "慢病管理系统中的患者 Id"}, "mbglProjectID": {"type": "string", "description": "慢病管理系统中的项目 Id"}, "visitDate": {"type": "string", "description": "访视日期"}, "mainVisitContent": {"type": "array", "description": "主要访视内容", "default": [], "items": {"type": "string", "enum": ["知情", "随机", "出组"]}}, "mainVisitContentOtherInfo": {"type": "string", "description": "主要访视内容补充信息", "nullable": true}, "isUnplannedVisit": {"type": "boolean", "description": "是否为计划外访视"}, "crcName": {"type": "string", "description": "CRC 名称", "nullable": true}}, "required": ["mbglUserID", "mbglProjectID", "visitDate", "mainVisitContent", "mainVisitContentOtherInfo", "isUnplannedVisit", "crcName"]}, "CRCRegisterSendVerificationCodeRequestDto": {"type": "object", "properties": {"phone": {"type": "string", "description": "手机号"}, "username": {"type": "string", "description": "姓名"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["phone", "username", "email"]}, "CRCRegisterRequestDto": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}, "phone": {"type": "string", "description": "手机号"}, "email": {"type": "string", "description": "邮箱"}, "verificationCode": {"type": "string", "description": "验证码"}}, "required": ["username", "password", "phone", "email", "verificationCode"]}, "CRCWhitelistQueryResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID"}, "name": {"type": "string", "description": "姓名"}, "phone": {"type": "string", "description": "电话号码"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["id", "name", "phone", "email"]}, "CRCWhitelistCreateRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "姓名"}, "phone": {"type": "string", "description": "电话号码"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["name", "phone", "email"]}, "CRCUpdateRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "姓名"}, "phone": {"type": "string", "description": "电话号码"}, "email": {"type": "string", "description": "邮箱"}}, "required": ["name", "phone", "email"]}, "CreateProjectRequestDto": {"type": "object", "properties": {"projectFullname": {"type": "string", "description": "项目全称"}, "projectCode": {"type": "string", "description": "项目代码"}, "launchDate": {"format": "date-time", "type": "string", "description": "项目启动日期"}, "sponsorId": {"type": "string", "description": "项目申办方ID"}, "objectTypeId": {"type": "string", "description": "项目对象类型ID"}, "diseaseIds": {"description": "项目疾病ID", "type": "array", "items": {"type": "number"}}, "phaseId": {"type": "string", "description": "项目阶段ID"}, "statusId": {"type": "string", "description": "项目状态ID"}}, "required": ["projectFullname", "projectCode", "launchDate", "sponsorId", "objectTypeId", "diseaseIds", "phaseId", "statusId"]}, "CreateProjectResponseDto": {"type": "object", "properties": {"projectID": {"type": "string", "description": "项目ID"}}, "required": ["projectID"]}, "SponsorResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "申办方ID"}, "name": {"type": "string", "description": "申办方名称"}}, "required": ["id", "name"]}, "ObjectTypeResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "研究对象类型ID"}, "name": {"type": "string", "description": "研究对象类型名称"}}, "required": ["id", "name"]}, "ProjectPhaseResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "项目阶段ID"}, "name": {"type": "string", "description": "项目阶段名称"}}, "required": ["id", "name"]}, "StatusResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "项目状态ID"}, "name": {"type": "string", "description": "项目状态名称"}}, "required": ["id", "name"]}, "DiseaseResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "疾病ID"}, "name": {"type": "string", "description": "疾病名称"}}, "required": ["id", "name"]}, "ProjectMemberUserProfileResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户档案ID"}, "phone": {"type": "string", "description": "用户档案手机号"}, "lastName": {"type": "string", "description": "用户档案姓氏"}, "firstName": {"type": "string", "description": "用户档案名字"}}, "required": ["id", "phone", "lastName", "firstName"]}, "ProjectMemberUserResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户ID"}, "username": {"type": "string", "description": "用户名称"}, "email": {"type": "string", "description": "用户邮箱"}, "profile": {"description": "用户档案", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ProjectMemberUserProfileResponseDto"}]}}, "required": ["id", "username", "email", "profile"]}, "ProjectMemberRoleResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "角色ID"}, "name": {"type": "string", "description": "角色名称"}}, "required": ["id", "name"]}, "GetProjectMemberResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "成员ID"}, "user": {"description": "成员用户", "allOf": [{"$ref": "#/components/schemas/ProjectMemberUserResponseDto"}]}, "roles": {"description": "成员角色", "type": "array", "items": {"$ref": "#/components/schemas/ProjectMemberRoleResponseDto"}}, "isBlinded": {"type": "boolean", "description": "是否盲态", "nullable": true}}, "required": ["id", "user", "roles", "isBlinded"]}, "GetAllResearchProjectResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "研究项目ID"}, "name": {"type": "string", "description": "研究项目名称", "nullable": true}, "sponsor": {"description": "研究项目申办方", "nullable": true, "allOf": [{"$ref": "#/components/schemas/SponsorResponseDto"}]}, "code": {"type": "string", "description": "研究项目代码", "nullable": true}, "objectType": {"description": "研究项目对象类型", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ObjectTypeResponseDto"}]}, "projectPhase": {"description": "研究项目阶段", "allOf": [{"$ref": "#/components/schemas/ProjectPhaseResponseDto"}]}, "status": {"description": "研究项目状态", "allOf": [{"$ref": "#/components/schemas/StatusResponseDto"}]}, "diseases": {"description": "研究项目疾病", "type": "array", "items": {"$ref": "#/components/schemas/DiseaseResponseDto"}}, "members": {"description": "研究项目成员", "type": "array", "items": {"$ref": "#/components/schemas/GetProjectMemberResponseDto"}}, "launchDate": {"format": "date-time", "type": "string", "description": "研究项目启动日期", "nullable": true}, "endDate": {"format": "date-time", "type": "string", "description": "研究项目结束日期", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "研究项目创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "研究项目更新时间"}}, "required": ["id", "name", "sponsor", "code", "objectType", "projectPhase", "status", "diseases", "members", "launchDate", "endDate", "createdAt", "updatedAt"]}, "GetProjectDetailsResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "研究项目ID"}, "name": {"type": "string", "description": "研究项目名称", "nullable": true}, "fullName": {"type": "string", "description": "研究项目全称", "nullable": true}, "sponsor": {"description": "研究项目申办方", "nullable": true, "allOf": [{"$ref": "#/components/schemas/SponsorResponseDto"}]}, "code": {"type": "string", "description": "研究项目代码", "nullable": true}, "objectType": {"description": "研究项目对象类型", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ObjectTypeResponseDto"}]}, "projectPhase": {"description": "研究项目阶段", "allOf": [{"$ref": "#/components/schemas/ProjectPhaseResponseDto"}]}, "status": {"description": "研究项目状态", "allOf": [{"$ref": "#/components/schemas/StatusResponseDto"}]}, "diseases": {"description": "研究项目疾病", "type": "array", "items": {"$ref": "#/components/schemas/DiseaseResponseDto"}}, "members": {"description": "研究项目成员", "type": "array", "items": {"$ref": "#/components/schemas/GetProjectMemberResponseDto"}}, "launchDate": {"format": "date-time", "type": "string", "description": "研究项目启动日期", "nullable": true}, "endDate": {"format": "date-time", "type": "string", "description": "研究项目结束日期", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "研究项目创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "研究项目更新时间"}}, "required": ["id", "name", "fullName", "sponsor", "code", "objectType", "projectPhase", "status", "diseases", "members", "launchDate", "endDate", "createdAt", "updatedAt"]}, "UpdateResearchProjectRequestDto": {"type": "object", "properties": {"projectFullname": {"type": "string", "description": "项目全称"}, "projectCode": {"type": "string", "description": "项目代码"}, "launchDate": {"format": "date-time", "type": "string", "description": "项目启动日期"}, "sponsorId": {"type": "string", "description": "项目申办方ID"}, "objectTypeId": {"type": "string", "description": "项目对象类型ID"}, "diseaseIds": {"description": "项目疾病ID", "type": "array", "items": {"type": "number"}}, "phaseId": {"type": "string", "description": "项目阶段ID"}, "statusId": {"type": "string", "description": "项目状态ID"}}, "required": ["projectFullname", "projectCode", "launchDate", "sponsorId", "objectTypeId", "diseaseIds", "phaseId", "statusId"]}, "UpdateResearchProjectResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "项目ID"}}, "required": ["id"]}, "AddMemberRequestDto": {"type": "object", "properties": {}}, "AddMemberResponseDto": {"type": "object", "properties": {}}, "UpdateProjectMemberRequestDto": {"type": "object", "properties": {}}, "UpdateProjectMemberResponseDto": {"type": "object", "properties": {}}, "TempGetUserQueryResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户 ID"}, "name": {"type": "string", "description": "用户名"}, "phone": {"type": "string", "description": "用户电话"}}, "required": ["id", "name", "phone"]}, "CreateReferrerRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "推荐人的姓名", "example": "张三"}, "phone": {"type": "string", "description": "推荐人的电话", "example": "12345678901"}}, "required": ["name", "phone"]}, "CreateReferrerResponseDto": {"type": "object", "properties": {"referrerId": {"type": "string", "description": "返回创建的推荐人ID", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"}}, "required": ["referrerId"]}, "UpdateReferrerRequestDto": {"type": "object", "properties": {"name": {"type": "string", "description": "推荐人的姓名", "example": "张三"}, "phone": {"type": "string", "description": "推荐人的电话", "example": "12345678901"}}, "required": ["name", "phone"]}, "UpdateReferrerResponseDto": {"type": "object", "properties": {"referrerId": {"type": "string", "description": "返回更新的推荐人ID", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"}}, "required": ["referrerId"]}, "GetReferrersResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "推荐人ID"}, "name": {"type": "string", "description": "推荐人名称"}, "phone": {"type": "string", "description": "推荐人手机号"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "name", "phone", "createdAt", "updatedAt"]}, "CursorPaginatedGetReferrersResponseDto": {"type": "object", "properties": {"items": {"description": "当前页数据列表", "type": "array", "items": {"$ref": "#/components/schemas/GetReferrersResponseDto"}}, "nextCursor": {"type": "string", "description": "下一页的游标。如果为 null，表示没有更多数据。", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345", "nullable": true}, "previousCursor": {"type": "string", "description": "用于获取上一页数据的游标 (来自下一页响应的 previousCursor)", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345", "nullable": true}, "sortBy": {"type": "string", "description": "排序字段 (通常是游标字段)", "example": "createdAt"}, "sortOrder": {"type": "string", "description": "排序顺序", "enum": ["asc", "desc"], "default": "desc"}}, "required": ["items"]}, "CreateAssociationRecordDto": {"type": "object", "properties": {"referrerId": {"type": "string", "description": "推荐人 ID", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"}, "patientMBGLId": {"type": "number", "description": "慢病管理患者 ID", "example": 123}, "patientGCPMId": {"type": "string", "description": "GCPM 患者 UUID", "example": "yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy"}}, "required": ["referrerId", "patientMBGLId"]}, "UpdateAssociationRecordDto": {"type": "object", "properties": {"referrerId": {"type": "string", "description": "推荐人 ID", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"}, "patientMBGLId": {"type": "number", "description": "慢病管理患者 ID", "example": 123}, "patientGCPMId": {"type": "string", "description": "GCPM 患者 UUID", "example": "yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy"}}, "required": ["referrerId", "patientMBGLId"]}, "UpdateAssociationRecordResponseDto": {"type": "object", "properties": {"associationRecordId": {"type": "string", "description": "关联记录 ID"}}, "required": ["associationRecordId"]}, "ReferralDto": {"type": "object", "properties": {"id": {"type": "string", "description": "推荐人 ID"}, "name": {"type": "string", "description": "推荐人姓名", "example": "张三"}, "phone": {"type": "string", "description": "推荐人电话", "example": "13800138000"}}, "required": ["id", "name", "phone"]}, "PatientDto": {"type": "object", "properties": {"mbglId": {"type": "number", "description": "慢病管理患者 ID", "example": 9001, "nullable": true}, "gcpmId": {"type": "string", "description": "GCPM 患者 UUID", "nullable": true}, "name": {"type": "string", "description": "患者姓名"}, "phone": {"type": "string", "description": "患者电话"}}, "required": ["mbglId", "gcpmId", "name", "phone"]}, "GetAssociationRecordResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "关联记录 ID"}, "referrer": {"description": "推荐人", "allOf": [{"$ref": "#/components/schemas/ReferralDto"}]}, "patient": {"description": "患者", "allOf": [{"$ref": "#/components/schemas/PatientDto"}]}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "referrer", "patient", "createdAt", "updatedAt"]}, "CursorPaginatedGetAssociationRecordResponseDto": {"type": "object", "properties": {"items": {"description": "当前页数据列表", "type": "array", "items": {"$ref": "#/components/schemas/GetAssociationRecordResponseDto"}}, "nextCursor": {"type": "string", "description": "下一页的游标。如果为 null，表示没有更多数据。", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345", "nullable": true}, "previousCursor": {"type": "string", "description": "用于获取上一页数据的游标 (来自下一页响应的 previousCursor)", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345", "nullable": true}, "sortBy": {"type": "string", "description": "排序字段 (通常是游标字段)", "example": "createdAt"}, "sortOrder": {"type": "string", "description": "排序顺序", "enum": ["asc", "desc"], "default": "desc"}}, "required": ["items"]}, "GetNoReferrerMbglPatientsResponseDto": {"type": "object", "properties": {"mbglId": {"type": "string", "description": "慢病管理患者 ID"}, "patientName": {"type": "string", "description": "患者姓名"}, "patientPhone": {"type": "string", "description": "患者手机号", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间", "nullable": true}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间", "nullable": true}}, "required": ["mbglId", "patientName", "patientPhone", "createdAt", "updatedAt"]}, "CursorPaginatedGetNoReferrerMbglPatientsResponseDto": {"type": "object", "properties": {"items": {"description": "当前页数据列表", "type": "array", "items": {"$ref": "#/components/schemas/GetNoReferrerMbglPatientsResponseDto"}}, "nextCursor": {"type": "string", "description": "下一页的游标。如果为 null，表示没有更多数据。", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345", "nullable": true}, "previousCursor": {"type": "string", "description": "用于获取上一页数据的游标 (来自下一页响应的 previousCursor)", "example": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345", "nullable": true}, "sortBy": {"type": "string", "description": "排序字段 (通常是游标字段)", "example": "user_id"}, "sortOrder": {"type": "string", "description": "排序顺序", "enum": ["asc", "desc"], "default": "desc"}}, "required": ["items"]}}}}