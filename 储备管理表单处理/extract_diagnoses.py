import pandas as pd
import os

def extract_unique_diagnoses(input_file, output_file):
    """
    从表单中提取所有不重复的出院诊断并输出为CSV文件
    
    参数:
    input_file (str): 输入文件路径（Excel或CSV）
    output_file (str): 输出CSV文件路径
    """
    # 检查文件扩展名
    _, ext = os.path.splitext(input_file)
    
    # 根据文件扩展名读取数据
    if ext.lower() in ['.xlsx', '.xls']:
        df = pd.read_excel(input_file)
    elif ext.lower() == '.csv':
        df = pd.read_csv(input_file, encoding='utf-8')
    else:
        raise ValueError(f"不支持的文件格式: {ext}")
    
    # 提取出院诊断列
    diagnosis_columns = [col for col in df.columns if '出院诊断' in col and col != '出院诊断医生']
    
    # 创建一个集合来存储所有不重复的诊断
    all_diagnoses = set()
    
    # 遍历所有诊断列，收集不重复的诊断
    for col in diagnosis_columns:
        # 过滤掉空值和NaN
        valid_diagnoses = df[col].dropna().astype(str)
        # 只添加非空字符串
        valid_diagnoses = [diag.strip() for diag in valid_diagnoses if diag.strip()]
        all_diagnoses.update(valid_diagnoses)
    
    # 将集合转换为列表并排序
    unique_diagnoses_list = sorted(list(all_diagnoses))
    
    # 创建一个新的DataFrame
    result_df = pd.DataFrame({'疾病诊断': unique_diagnoses_list})
    
    # 保存到CSV文件
    result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"已成功提取{len(unique_diagnoses_list)}个不重复的出院诊断并保存到 {output_file}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='从表单中提取不重复的出院诊断')
    parser.add_argument('input_file', help='输入文件路径（Excel或CSV）')
    parser.add_argument('--output_file', help='输出CSV文件路径', default='不重复出院诊断列表.csv')
    
    args = parser.parse_args()
    
    extract_unique_diagnoses(args.input_file, args.output_file) 