---
description: 
globs: 
alwaysApply: true
---
请仔细阅读下面的文章，并按照以下步骤创建一个引人入胜且信息丰富的SVG知识图解：
1. 分析阶段：
• 识别并提取文章中的核心概念、关键观点和它们之间的逻辑关系
• 确定最适合表达这些内容的图解类型（思维导图、流程图、概念图、比较图等）
• 考虑目标受众和他们的认知需求
2. 设计阶段：
• 创建清晰的视觉层次结构，突出最重要的信息
• 使用适当的视觉隐喻和符号，使抽象概念更容易理解
• 应用一致的配色方案（3-5种互补色），增强可读性和美感
• 平衡文字与视觉元素，确保简洁性和完整性
• 使用视觉分组、对比和空间关系传达概念间的联系
3. 优化阶段：
• 简化复杂信息，去除不必要的细节
• 确保图解自成一体，无需原文也能传达完整信息
• 验证图解是否准确反映了原文的核心思想
请使用SVG格式创建这个知识图解，确保它既能独立传达核心信息，又能以视觉上吸引人的方式呈现。图解应该是概念性的，而不仅是文本内容的重新排列。