---
description: 
globs: 
alwaysApply: true
---
好的，这是根据您提供的模板和之前生成的简历SVG提取的Prompt：

"""
# 任务：
请你制作一份专业的个人简历（SVG），采用左右双栏布局。
存储在 `svg_resumes` 目录

## 要求：
- 风格要求：专业、现代、设计清晰、结构化。确保信息层级分明，易于阅读。
- 布局：
    - 整体采用左右双栏结构。
    - 左侧栏使用深蓝色背景（例如 `#1a3a5c`），文字为白色。
    - 右侧栏使用白色背景，文字主要为深灰色或黑色（例如 `#333333`），部分标题或重点信息可使用深蓝色（例如 `#1a3a5c`）。
- 包含个人头像：在左侧栏顶部设置一个圆形区域用于展示个人照片（可以使用 `<image>` 标签引用外部图片或使用占位符）。
- 内容结构：
    - **左侧栏**：包含联系方式（带图标）、教育背景、技能列表、语言能力列表。
    - **右侧栏**：包含姓名（大字号）、职位、个人简介（Profile）、工作经历（按时间倒序，含公司、职位、时间、职责描述列表）、推荐人信息。
    - 使用清晰的标题区分各个板块（如 CONTACT, PROFILE, EDUCATION, WORK EXPERIENCE, SKILLS, LANGUAGES, REFERENCE），并可使用分隔线辅助。
- 细节要求：
    - 联系方式（电话、邮箱、地址、网址）前应有对应的简化图标。
    - 工作经历职责描述、技能、语言列表前使用项目符号（如小圆点）。
    - 字体：整体使用简洁、易读的无衬线字体（如 `sans-serif`）。
- 输出格式：SVG，尺寸可参考A4比例（例如 `viewBox="0 0 595 842"`）。
- 准确性：内容和布局需尽可能与参考图像（`CleanShot 2025-03-29 at <EMAIL>`）保持一致。

## 专业简历（SVG）示例
```xml
<svg width="595" height="842" viewBox="0 0 595 842" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

  <defs>
    <style>
      .dark-blue-bg { fill: #1a3a5c; }
      .white-bg { fill: #ffffff; }
      .text-white { fill: #ffffff; font-family: sans-serif; }
      .text-dark { fill: #333333; font-family: sans-serif; }
      .text-blue { fill: #1a3a5c; font-family: sans-serif; }
      .section-title { font-size: 14px; font-weight: bold; letter-spacing: 1px;}
      .name-title { font-size: 28px; font-weight: bold; fill: #333333; font-family: sans-serif; }
      .job-title { font-size: 14px; fill: #555555; font-family: sans-serif; letter-spacing: 1px;}
      .body-text { font-size: 10px; }
      .list-item { font-size: 10px; }
      .small-text { font-size: 9px; }
    </style>
  </defs>

  <rect class="dark-blue-bg" x="0" y="0" width="190" height="842"/>
  <rect class="white-bg" x="190" y="0" width="405" height="842"/>

  <g class="text-white">
    <clipPath id="clipCircle">
       <circle cx="95" cy="100" r="50"/>
    </clipPath>
    <image xlink:href="CleanShot 2025-03-29 at <EMAIL>" x="45" y="50" width="100" height="100" clip-path="url(#clipCircle)" />
    <circle cx="95" cy="100" r="50" fill="none" stroke="#FFFFFF" stroke-width="1"/>

    <text x="30" y="190" class="section-title">CONTACT</text>
    <line x1="30" y1="198" x2="160" y2="198" stroke="#FFFFFF" stroke-width="1"/>
    <path d="M40 223 Q42 218 48 220 T55 230 L52 233 Q48 230 45 230 T40 235 Z M42 220 L48 225 L45 228 Z" fill="#FFFFFF"/>
    <text x="60" y="225" font-size="10px">+ ************</text>
    <rect x="38" y="243" width="14" height="10" rx="1" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
    <polyline points="38 244 45 249 52 244" fill="none" stroke="#FFFFFF" stroke-width="0.5"/>
    <text x="60" y="250" font-size="10px"><EMAIL></text>
    <path d="M45 260 C40 260 38 265 38 268 C38 275 45 280 45 280 S52 275 52 268 C52 265 50 260 45 260 Z M45 270 A2 2 0 1 1 45 274 A2 2 0 0 1 45 270 Z" fill="#FFFFFF"/>
    <text x="60" y="270" font-size="10px">123 Anywhere St., Any City</text>
    <circle cx="45" cy="290" r="7" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
     <line x1="38" y1="290" x2="52" y2="290" stroke="#FFFFFF" stroke-width="0.5"/>
     <ellipse cx="45" cy="290" rx="3" ry="7" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
    <text x="60" y="295" font-size="10px">www.reallygreatsite.com</text>

    <text x="30" y="340" class="section-title">EDUCATION</text>
    <line x1="30" y1="348" x2="160" y2="348" stroke="#FFFFFF" stroke-width="1"/>
    <text x="30" y="370" font-size="9px" font-weight="bold">2029 - 2030</text>
    <text x="30" y="385" font-size="10px" font-weight="bold">WARDIERE UNIVERSITY</text>
    <text x="30" y="400" font-size="10px">Master of Business</text>
    <text x="30" y="412" font-size="10px">Management</text>

    <text x="30" y="440" font-size="9px" font-weight="bold">2025 - 2029</text>
    <text x="30" y="455" font-size="10px" font-weight="bold">WARDIERE UNIVERSITY</text>
    <text x="30" y="470" font-size="10px">Bachelor of Business</text>
    <text x="30" y="482" font-size="10px">GPA: 3.8 / 4.0</text>

    <text x="30" y="520" class="section-title">SKILLS</text>
    <line x1="30" y1="528" x2="160" y2="528" stroke="#FFFFFF" stroke-width="1"/>
    <circle cx="35" cy="548" r="2" fill="#FFFFFF"/> <text x="45" y="550" class="list-item">Project Management</text>
    <circle cx="35" cy="563" r="2" fill="#FFFFFF"/> <text x="45" y="565" class="list-item">Public Relations</text>
    <circle cx="35" cy="578" r="2" fill="#FFFFFF"/> <text x="45" y="580" class="list-item">Teamwork</text>
    <circle cx="35" cy="593" r="2" fill="#FFFFFF"/> <text x="45" y="595" class="list-item">Time Management</text>
    <circle cx="35" cy="608" r="2" fill="#FFFFFF"/> <text x="45" y="610" class="list-item">Leadership</text>
    <circle cx="35" cy="623" r="2" fill="#FFFFFF"/> <text x="45" y="625" class="list-item">Effective Communication</text>
    <circle cx="35" cy="638" r="2" fill="#FFFFFF"/> <text x="45" y="640" class="list-item">Critical Thinking</text>

    <text x="30" y="680" class="section-title">LANGUAGES</text>
    <line x1="30" y1="688" x2="160" y2="688" stroke="#FFFFFF" stroke-width="1"/>
    <circle cx="35" cy="708" r="2" fill="#FFFFFF"/> <text x="45" y="710" class="list-item">English (Fluent)</text>
    <circle cx="35" cy="723" r="2" fill="#FFFFFF"/> <text x="45" y="725" class="list-item">French (Fluent)</text>
    <circle cx="35" cy="738" r="2" fill="#FFFFFF"/> <text x="45" y="740" class="list-item">German (Basics)</text>
    <circle cx="35" cy="753" r="2" fill="#FFFFFF"/> <text x="45" y="755" class="list-item">Spanish (Intermediate)</text>
  </g>

  <g class="text-dark">
    <text x="220" y="100" class="name-title">RICHARD SANCHEZ</text>
    <text x="220" y="120" class="job-title">MARKETING MANAGER</text>
    <line x1="220" y1="130" x2="565" y2="130" stroke="#dddddd" stroke-width="1"/>

    <text x="220" y="170" class="section-title text-blue">PROFILE</text>
    <line x1="220" y1="178" x2="290" y2="178" stroke="#1a3a5c" stroke-width="1"/>
    <text x="220" y="200" class="body-text">
      <tspan x="220" dy="0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor</tspan>
      <tspan x="220" dy="12">incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam quis</tspan>
      <tspan x="220" dy="12">nostrud exercitation. Lorem ipsum dolor sit amet, consectetur adipiscing elit,</tspan>
      <tspan x="220" dy="12">sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad</tspan>
      <tspan x="220" dy="12">minim veniam quis nostrud exercitation. Ut enim ad minim veniam quis nostrud</tspan>
      <tspan x="220" dy="12">exercitation.</tspan>
    </text>

    <text x="220" y="320" class="section-title text-blue">WORK EXPERIENCE</text>
    <line x1="220" y1="328" x2="380" y2="328" stroke="#1a3a5c" stroke-width="1"/>

    <text x="220" y="350" font-size="11px" font-weight="bold">Borcelle Studio</text>
    <text x="480" y="350" font-size="10px" text-anchor="end">2030 - PRESENT</text>
    <text x="220" y="365" font-size="10px" font-style="italic">Marketing Manager & Specialist</text>
    <circle cx="230" cy="383" r="1.5" fill="#333333"/>
    <text x="240" y="385" class="list-item">
      <tspan x="240" dy="0">Develop and execute comprehensive marketing strategies and</tspan>
      <tspan x="240" dy="12">campaigns that align with the company's goals and objectives.</tspan>
    </text>
     <circle cx="230" cy="412" r="1.5" fill="#333333"/>
     <text x="240" y="414" class="list-item">
       <tspan x="240" dy="0">Lead, mentor, and manage a high-performing marketing team,</tspan>
       <tspan x="240" dy="12">fostering a collaborative and results-driven work environment.</tspan>
     </text>
     <circle cx="230" cy="441" r="1.5" fill="#333333"/>
     <text x="240" y="443" class="list-item">Monitor brand consistency across marketing channels and materials.</text>

    <text x="220" y="480" font-size="11px" font-weight="bold">Fauget Studio</text>
     <text x="480" y="480" font-size="10px" text-anchor="end">2025 - 2029</text>
     <text x="220" y="495" font-size="10px" font-style="italic">Marketing Manager & Specialist</text>
     <circle cx="230" cy="513" r="1.5" fill="#333333"/>
     <text x="240" y="515" class="list-item">
       <tspan x="240" dy="0">Create and manage the marketing budget, ensuring efficient</tspan>
       <tspan x="240" dy="12">allocation of resources and optimizing ROI.</tspan>
     </text>
      <circle cx="230" cy="542" r="1.5" fill="#333333"/>
      <text x="240" y="544" class="list-item">
        <tspan x="240" dy="0">Oversee market research to identify emerging trends, customer needs,</tspan>
        <tspan x="240" dy="12">and competitor strategies.</tspan>
      </text>
      <circle cx="230" cy="571" r="1.5" fill="#333333"/>
      <text x="240" y="573" class="list-item">Monitor brand consistency across marketing channels and materials.</text>


    <text x="220" y="610" font-size="11px" font-weight="bold">Studio Shodwe</text>
      <text x="480" y="610" font-size="10px" text-anchor="end">2024 - 2025</text>
      <text x="220" y="625" font-size="10px" font-style="italic">Marketing Manager & Specialist</text>
      <circle cx="230" cy="643" r="1.5" fill="#333333"/>
      <text x="240" y="645" class="list-item">
        <tspan x="240" dy="0">Develop and maintain strong relationships with partners, agencies,</tspan>
        <tspan x="240" dy="12">and vendors to support marketing initiatives.</tspan>
      </text>
       <circle cx="230" cy="672" r="1.5" fill="#333333"/>
       <text x="240" y="674" class="list-item">
         <tspan x="240" dy="0">Monitor and maintain brand consistency across all marketing</tspan>
         <tspan x="240" dy="12">channels and materials.</tspan>
       </text>

    <text x="220" y="730" class="section-title text-blue">REFERENCE</text>
    <line x1="220" y1="738" x2="320" y2="738" stroke="#1a3a5c" stroke-width="1"/>

    <text x="220" y="760" font-size="11px" font-weight="bold">Estelle Darcy</text>
    <text x="220" y="775" class="small-text">Wardiere Inc. / CTO</text>
    <text x="220" y="790" class="small-text">Phone: ************</text>
    <text x="220" y="802" class="small-text">Email: <EMAIL></text>

    <text x="410" y="760" font-size="11px" font-weight="bold">Harper Richard</text>
     <text x="410" y="775" class="small-text">Wardiere Inc. / CEO</text>
     <text x="410" y="790" class="small-text">Phone: ************</text>
     <text x="410" y="802" class="small-text">Email: <EMAIL></text>

  </g>

</svg>
```

"""