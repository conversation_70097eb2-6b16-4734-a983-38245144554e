{"name": "bare-fs", "version": "2.3.5", "description": "Native file system for Javascript", "main": "index.js", "files": ["index.js", "promises.js", "binding.c", "binding.js", "CMakeLists.txt", "prebuilds"], "addon": true, "scripts": {"test": "standard && bare test.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-fs.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "homepage": "https://github.com/holepunchto/bare-fs#readme", "dependencies": {"bare-events": "^2.0.0", "bare-path": "^2.0.0", "bare-stream": "^2.0.0"}, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}}