{"version": 3, "file": "browser-data.js", "sourceRoot": "", "sources": ["../../../src/browser-data/browser-data.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,mBAAmB,MAAM,4BAA4B,CAAC;AAClE,OAAO,KAAK,MAAM,MAAM,aAAa,CAAC;AACtC,OAAO,KAAK,YAAY,MAAM,mBAAmB,CAAC;AAClD,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;AACxC,OAAO,EACL,OAAO,EACP,eAAe,EACf,UAAU,EACV,oBAAoB,GAErB,MAAM,YAAY,CAAC;AAIpB,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,kBAAkB;IACvD,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,kBAAkB;IACrE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,kBAAkB;IAC3C,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,kBAAkB;IAC/C,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,kBAAkB;CAC9C,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,mBAAmB;IACxD,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,mBAAmB;IACtE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,mBAAmB;IAC5C,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,mBAAmB;IAChD,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,mBAAmB;CAC/C,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,sBAAsB;IAC3D,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,sBAAsB;IACzE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,sBAAsB;IAC/C,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,sBAAsB;IACnD,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,sBAAsB;CAClD,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAChC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,eAAe;IACpD,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,eAAe;IAClE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,eAAe;IACxC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,eAAe;IAC5C,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,eAAe;CAC3C,CAAC;AAEF,OAAO,EAAC,OAAO,EAAE,eAAe,EAAE,oBAAoB,EAAC,CAAC;AAExD;;GAEG;AACH,KAAK,UAAU,2BAA2B,CACxC,OAAgB,EAChB,QAAyB,EACzB,GAAe;IAEf,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,OAAO,CAAC,OAAO;YAClB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACtE,KAAK,UAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACnE,KAAK,UAAU,CAAC,OAAO;oBACrB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACtE,KAAK,UAAU,CAAC,UAAU;oBACxB,OAAO,MAAM,OAAO,CAAC,cAAc,CACjC,OAAO,CAAC,cAAc,CAAC,UAAU,CAClC,CAAC;gBACJ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACrE,KAAK,UAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBAClE,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE,+BAA+B,CAAC,CAAC;YACzE,CAAC;QACH,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,UAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAChE,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,UAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;gBAC/D,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,UAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,UAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,UAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE,8BAA8B,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QACD,KAAK,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YAC1B,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBACxE,KAAK,UAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBACtE,KAAK,UAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;gBACrE,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBACxE,KAAK,UAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,UAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,UAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CACb,GAAG,GAAG,CAAC,WAAW,EAAE,oCAAoC,CACzD,CAAC;YACN,CAAC;QACH,CAAC;QACD,KAAK,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACjC,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,oBAAoB,CAAC,MAAM,CAC5B,CAAC;gBACJ,KAAK,UAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,oBAAoB,CAAC,IAAI,CAC1B,CAAC;gBACJ,KAAK,UAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,oBAAoB,CAAC,GAAG,CACzB,CAAC;gBACJ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,oBAAoB,CAAC,MAAM,CAC5B,CAAC;gBACJ,KAAK,UAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,UAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,UAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,6CAA6C,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QACD,KAAK,OAAO,CAAC,QAAQ;YACnB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACjD,KAAK,UAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,GAAG,CAAC;gBACpB,KAAK,UAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,UAAU,CAAC,IAAI,CAAC;gBACrB,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CACb,GAAG,GAAG,uDAAuD,CAC9D,CAAC;YACN,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,OAAgB,EAChB,QAAyB,EACzB,GAAW;IAEX,MAAM,UAAU,GAAG,GAAiB,CAAC;IACrC,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACnD,OAAO,MAAM,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,OAAO,CAAC,OAAO;YAClB,OAAO,GAAG,CAAC;QACb,KAAK,OAAO,CAAC,MAAM;YACjB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,OAAO,GAAG,CAAC;QACb,KAAK,OAAO,CAAC,YAAY;YACvB,MAAM,kBAAkB,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAClE,IAAI,kBAAkB,EAAE,CAAC;gBACvB,OAAO,kBAAkB,CAAC;YAC5B,CAAC;YACD,OAAO,GAAG,CAAC;QACb,KAAK,OAAO,CAAC,mBAAmB;YAC9B,MAAM,yBAAyB,GAC7B,MAAM,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,yBAAyB,EAAE,CAAC;gBAC9B,OAAO,yBAAyB,CAAC;YACnC,CAAC;YACD,OAAO,GAAG,CAAC;QACb,KAAK,OAAO,CAAC,QAAQ;YACnB,OAAO,GAAG,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,OAAgB,EAChB,IAAoB;IAEpB,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,OAAO,CAAC,OAAO;YAClB,OAAO,MAAM,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,OAAO,CAAC,MAAM,CAAC;QACpB,KAAK,OAAO,CAAC,QAAQ;YACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,OAAO,MAAM,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B,CACzC,OAAgB,EAChB,QAAyB,EACzB,OAA6B;IAE7B,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,OAAO,CAAC,YAAY,CAAC;QAC1B,KAAK,OAAO,CAAC,mBAAmB,CAAC;QACjC,KAAK,OAAO,CAAC,OAAO,CAAC;QACrB,KAAK,OAAO,CAAC,QAAQ;YACnB,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,OAAO,CAChE,CAAC;QACJ,KAAK,OAAO,CAAC,MAAM;YACjB,OAAO,MAAM,CAAC,2BAA2B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,oBAAoB,CAClC,OAAgB;IAEhB,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC"}