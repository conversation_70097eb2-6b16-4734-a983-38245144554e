{"version": 3, "file": "CdpConnection.js", "sourceRoot": "", "sources": ["../../../src/cdp/CdpConnection.ts"], "names": [], "mappings": ";AAmBA,OAAO,EAAC,OAAO,EAAC,MAAM,iBAAiB,CAAC;AAIxC,OAAO,EAAC,UAAU,EAAE,eAAe,EAAiB,MAAM,gBAAgB,CAAC;AAc3E;;;;;GAKG;AACH,MAAM,OAAO,mBAAmB;IAC9B,MAAM,CAAU,kBAAkB,GAAG,GAAG,OAAO,CAAC,GAAG,SAAkB,CAAC;IACtE,MAAM,CAAU,kBAAkB,GAAG,GAAG,OAAO,CAAC,GAAG,SAAkB,CAAC;IAE7D,qBAAqB,CAAkB;IACvC,UAAU,CAAY;IAE/B;yDACqD;IAC5C,kBAAkB,GAAG,IAAI,GAAG,EAGlC,CAAC;IACK,iBAAiB,GAAG,IAAI,GAAG,EAAwB,CAAC;IACpD,OAAO,CAAY;IAC5B,OAAO,GAAG,CAAC,CAAC;IAEZ,YAAY,SAAoB,EAAE,MAAiB;QACjD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE9C,sCAAsC;QACtC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAChE,CAAC;IAED,4CAA4C;IAC5C,KAAK;QACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,KAAK,MAAM,CAAC,EAAE,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,MAAM,EAAC,SAAS,EAAC,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAC9D,8BAA8B,CAC/B,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,SAAoC;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,WAAW,CACT,MAAiB,EACjB,MAA6D,EAC7D,SAAqC;QAErC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE;gBAC7B,SAAS;gBACT,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,IAAI,UAAU,CACnB,GAAG,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IACjC,SAAS,IAAI,EACf,wDAAwD,CACzD;aACF,CAAC,CAAC;YACH,MAAM,UAAU,GAA0B,EAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC;YAC/D,IAAI,SAAS,EAAE,CAAC;gBACd,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YACnC,CAAC;YAED,KAAK,IAAI,CAAC,UAAU;iBACjB,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACxC,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAChB,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBAC1C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC;YACL,IAAI,CAAC,OAAO,EAAE,CAAC,EAAmB,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU,GAAG,CAAC,IAAY,EAAE,EAAE;QAC5B,MAAM,OAAO,GAAoB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC,EAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAEhE,6CAA6C;QAC7C,4CAA4C;QAC5C,IAAI,OAAO,CAAC,MAAM,KAAK,yBAAyB,EAAE,CAAC;YACjD,MAAM,EAAC,SAAS,EAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,OAAO,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;YAC7B,2BAA2B;YAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACpC,CAAC;qBAAM,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;oBACzB,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACxC,OAAO,CAAC,SAAS,IAAI,SAAS,CAC/B,CAAC;YACF,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;YAEnD,6CAA6C;YAC7C,2BAA2B;YAC3B,IAAI,OAAO,CAAC,MAAM,KAAK,2BAA2B,EAAE,CAAC;gBACnD,MAAM,EAAC,SAAS,EAAC,GAAG,OAAO,CAAC,MAAM,CAAC;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACtD,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC1C,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,CAAC;gBACD,4DAA4D;gBAC5D,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC;oBACvD,IAAI,QAAQ,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wBACrC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF;;;;;OAKG;IACH,gBAAgB,CACd,SAAgD;QAEhD,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAClD,OAAO,SAAS,CAAC;IACnB,CAAC"}