{"version": 3, "file": "Transport.js", "sourceRoot": "", "sources": ["../../../src/bidiTab/Transport.ts"], "names": [], "mappings": ";AAuBA,OAAO,EAAC,OAAO,EAAC,MAAM,iBAAiB,CAAC;AAGxC,OAAO,EAAC,GAAG,EAAC,MAAM,oBAAoB,CAAC;AAEvC,MAAM,OAAO,mBAAmB;IAC9B,MAAM,CAAU,kBAAkB,GAAG,GAAG,OAAO,CAAC,IAAI,SAAkB,CAAC;IACvE,MAAM,CAAU,kBAAkB,GAAG,GAAG,OAAO,CAAC,IAAI,SAAkB,CAAC;IAEvE,UAAU,GAAqD,IAAI,CAAC;IAEpE;QACE,MAAM,CAAC,aAAa,GAAG,CAAC,OAAe,EAAE,EAAE;YACzC,GAAG,CAAC,EAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,EAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAC/D,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,CAAU,EAAE,CAAC;gBACpB,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAW,CAAC,CAAC;gBAC9D,kDAAkD;gBAClD,IAAI,CAAC,iBAAiB,CAAC,OAAO,sDAA6B,KAAK,EAAE,IAAI,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,SAAuD;QAClE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,WAAW,CAAC,OAA6B;QACvC,GAAG,CAAC,EAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED,iBAAiB,CACf,gBAAwB,EACxB,SAAoB,EACpB,KAAY,EACZ,OAAwB;QAExB,MAAM,aAAa,GAAG,EAAmB,CAAC,iBAAiB,CACzD,gBAAgB,EAChB,SAAS,EACT,KAAK,CACN,CAAC;QAEF,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC;gBACf,GAAG,aAAa;gBAChB,OAAO;aACR,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,KAAc;QAChC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,OAAO,KAAK,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,iBAAiB,CACtB,OAAe,EACf,SAAoB,EACpB,KAAY;QAEZ,4DAA4D;QAC5D,2DAA2D;QAC3D,IAAI,SAAS,CAAC;QACd,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpC,IACE,EAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,QAAQ;gBACtD,IAAI,IAAI,OAAO,EACf,CAAC;gBACD,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;YACzB,CAAC;QACH,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QAEV,OAAO;YACL,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,SAAS;YACb,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAAe;QACtC,IAAI,OAA6B,CAAC;QAClC,IAAI,CAAC;YACH,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,IAAI,GAAG,EAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,8CAA8C;QAC9C,MAAM,EAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAC,GAAG,OAAO,CAAC;QAErC,MAAM,MAAM,GAAG,EAAmB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YAC3D,4CAA4C;YAC5C,kDAAkD;YAClD,MAAM,IAAI,KAAK,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,UAAU,GAAG,EAAmB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,kCAAkC,UAAU,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,UAAU,GAAG,EAAmB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,kCAAkC,UAAU,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,MAAM,WAAW,GAAG,EAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC9D,IAAI,WAAW,KAAK,QAAQ,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;YACpE,CAAC;YACD,6DAA6D;YAC7D,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC;gBACnB,OAAO,GAAG,SAAS,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,EAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAyB,CAAC;IAC/D,CAAC;;;AAGH,MAAM,OAAO,kBAAkB;IAC7B,UAAU,GAAuC,IAAI,CAAC;IAEtD;QACE,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,OAAe,EAAE,EAAE;YACzC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,SAAmD;QAC9D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;IAC9B,CAAC;CACF"}