{"version": 3, "file": "ErrorResponse.js", "sourceRoot": "", "sources": ["../../../src/protocol/ErrorResponse.ts"], "names": [], "mappings": "AAmBA,MAAM,OAAO,SAAU,SAAQ,KAAK;IAEzB;IACS;IACT;IAHT,YACS,KAAgB,EACP,OAAe,EACxB,UAAmB;QAE1B,KAAK,EAAE,CAAC;QAJD,UAAK,GAAL,KAAK,CAAW;QACP,YAAO,GAAP,OAAO,CAAQ;QACxB,eAAU,GAAV,UAAU,CAAS;IAG5B,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,OAAO;YACL,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,SAAS;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,wBAAyB,SAAQ,SAAS;IACrD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,qDAA4B,OAAO,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;CACF;AAED,MAAM,OAAO,wBAAyB,SAAQ,SAAS;IACrD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,qDAA4B,OAAO,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;CACF;AAED,MAAM,OAAO,yBAA0B,SAAQ,SAAS;IACtD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,wDAA6B,OAAO,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;CACF;AAED,MAAM,OAAO,8BAA+B,SAAQ,SAAS;IAC3D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,oEAAkC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC9D,CAAC;CACF;AAED,MAAM,OAAO,oBAAqB,SAAQ,SAAS;IACjD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,8CAAwB,OAAO,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;CACF;AAED,MAAM,OAAO,sBAAuB,SAAQ,SAAS;IACnD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,kDAA0B,OAAO,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;CACF;AAED,MAAM,OAAO,oBAAqB,SAAQ,SAAS;IACjD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,8CAAwB,OAAO,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;CACF;AAED,MAAM,OAAO,qBAAsB,SAAQ,SAAS;IAClD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,gDAAyB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrD,CAAC;CACF;AAED,MAAM,OAAO,2BAA4B,SAAQ,SAAS;IACxD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,6DAA+B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC3D,CAAC;CACF;AAED,MAAM,OAAO,wBAAyB,SAAQ,SAAS;IACrD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,sDAA4B,OAAO,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;CACF;AAED,MAAM,OAAO,mBAAoB,SAAQ,SAAS;IAChD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,4CAAuB,OAAO,EAAE,UAAU,CAAC,CAAC;IACnD,CAAC;CACF;AAED,MAAM,OAAO,sBAAuB,SAAQ,SAAS;IACnD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,kDAA0B,OAAO,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;CACF;AAED,MAAM,OAAO,qBAAsB,SAAQ,SAAS;IAClD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,gDAAyB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrD,CAAC;CACF;AAED,MAAM,OAAO,0BAA2B,SAAQ,SAAS;IACvD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,2DAA8B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,MAAM,OAAO,0BAA2B,SAAQ,SAAS;IACvD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,0DAA8B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,MAAM,OAAO,uBAAwB,SAAQ,SAAS;IACpD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,mDAA2B,OAAO,EAAE,UAAU,CAAC,CAAC;IACvD,CAAC;CACF;AAED,MAAM,OAAO,qBAAsB,SAAQ,SAAS;IAClD,YAAY,OAAe,EAAE,UAAU,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK;QACzD,KAAK,+CAAyB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrD,CAAC;CACF;AAED,MAAM,OAAO,8BAA+B,SAAQ,SAAS;IAC3D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,mEAAkC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC9D,CAAC;CACF;AAED,MAAM,OAAO,6BAA8B,SAAQ,SAAS;IAC1D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,iEAAiC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF;AAED,MAAM,OAAO,6BAA8B,SAAQ,SAAS;IAC1D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,+DAAiC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF;AAED,MAAM,OAAO,+BAAgC,SAAQ,SAAS;IAC5D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,qEAAmC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACF;AAED,MAAM,OAAO,0BAA2B,SAAQ,SAAS;IACvD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,2DAA8B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,MAAM,OAAO,6BAA8B,SAAQ,SAAS;IAC1D,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,kEAAiC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF;AAED,MAAM,OAAO,uCAAwC,SAAQ,SAAS;IACpE,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,oFAA2C,OAAO,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;CACF;AAED,MAAM,OAAO,4BAA6B,SAAQ,SAAS;IACzD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,8DAAgC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC5D,CAAC;CACF;AAED,MAAM,OAAO,2BAA4B,SAAQ,SAAS;IACxD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,6DAA+B,OAAO,EAAE,UAAU,CAAC,CAAC;IAC3D,CAAC;CACF"}