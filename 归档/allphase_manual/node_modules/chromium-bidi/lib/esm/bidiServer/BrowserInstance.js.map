{"version": 3, "file": "BrowserInstance.js", "sourceRoot": "", "sources": ["../../../src/bidiServer/BrowserInstance.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;GAgBG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EACL,4BAA4B,EAC5B,MAAM,GAEP,MAAM,qBAAqB,CAAC;AAC7B,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,SAAS,MAAM,IAAI,CAAC;AAE3B,OAAO,EAAC,mBAAmB,EAAC,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAC,kBAAkB,EAAC,MAAM,gCAAgC,CAAC;AAElE,OAAO,EAAC,yBAAyB,EAAC,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAC,kBAAkB,EAAC,MAAM,aAAa,CAAC;AAG/C,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAOpD;;;;;;;;GAQG;AACH,MAAM,OAAO,eAAe;IAC1B,oBAAoB,CAA4B;IAChD,eAAe,CAAU;IAEzB,MAAM,CAAC,KAAK,CAAC,GAAG,CACd,aAA4B,EAC5B,OAAgB;QAEhB,MAAM,UAAU,GAAG,MAAM,OAAO,CAC9B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,yBAAyB,CAAC,CAClD,CAAC;QACF,+FAA+F;QAC/F,MAAM,eAAe,GAAG;YACtB,oBAAoB;YACpB,8BAA8B;YAC9B,4BAA4B;YAC5B,wBAAwB;YACxB,6DAA6D;YAC7D,mGAAmG;YACnG,oBAAoB;YACpB,yBAAyB;YACzB,0BAA0B;YAC1B,uCAAuC;YACvC,qBAAqB;YACrB,4BAA4B;YAC5B,gBAAgB;YAChB,wBAAwB;YACxB,8BAA8B;YAC9B,qBAAqB;YACrB,mBAAmB,UAAU,EAAE;YAC/B,kBAAkB;YAClB,GAAG,aAAa,CAAC,UAAU;YAC3B,aAAa;SACd,CAAC;QAEF,MAAM,cAAc,GAClB,aAAa,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,eAAe,GAAG;YACtB,cAAc;YACd,IAAI,EAAE,eAAe;YACrB,GAAG,EAAE,OAAO,CAAC,GAAG;SACjB,CAAC;QAEF,aAAa,CAAC,mBAAmB,EAAE;YACjC,cAAc;YACd,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;QAE/C,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,iBAAiB,CACxD,4BAA4B,CAC7B,CAAC;QAEF,wDAAwD;QACxD,kBAAkB;QAClB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACtD,WAAW,CACZ,CAAC;QAEF,wCAAwC;QACxC,MAAM,eAAe,GAAG,MAAM,kBAAkB,EAAE,CAAC;QAEnD,qEAAqE;QACrE,MAAM,mBAAmB,GAAG,MAAM,yBAAyB,CAAC,MAAM,CAChE,aAAa,EACb,eAAe,EACf,OAAO,CACR,CAAC;QAEF,OAAO,IAAI,eAAe,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;IAClE,CAAC;IAED,YACE,mBAA8C,EAC9C,cAAuB;QAEvB,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,wBAAwB;QACxB,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAElC,iBAAiB;QACjB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,MAAc;QAC3C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,aAAa,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC;YAE5D,MAAM,EAAE,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;YAEjC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEzB,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBACjB,aAAa,CAAC,sBAAsB,CAAC,CAAC;gBAEtC,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBAC7C,MAAM,UAAU,GAAG,IAAI,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBACtD,OAAO,CAAC,UAAU,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}