{"version": 3, "file": "SessionProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/session/SessionProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAIH,+DAKuC;AAKvC,MAAa,gBAAgB;IAC3B,aAAa,CAAe;IAC5B,iBAAiB,CAAY;IAC7B,eAAe,CAAyC;IACxD,QAAQ,GAAG,KAAK,CAAC;IAEjB,YACE,YAA0B,EAC1B,gBAA2B,EAC3B,cAAsD;QAEtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED,MAAM;QACJ,OAAO,EAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,mBAAmB,EAAC,CAAC;IACtD,CAAC;IAED,kBAAkB,CAChB,mBAAgD;QAEhD,mFAAmF;QACnF,oDAAoD;QAEpD,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,mBAAmB,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3D,MAAM,MAAM,GAAG;gBACb,GAAG,mBAAmB,CAAC,WAAW;aACnC,CAAC;YACF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrC,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,IAAI,sCAAwB,CAChC,cAAc,GAAG,kDAAkD,CACpE,CAAC;gBACJ,CAAC;gBACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC;YAED,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC;QAED,MAAM,KAAK,GACT,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,QAAQ,CAAC;YAC1D,kBAAkB,CAAC,CAAC,CAAC;YACrB,EAAE,CAAC;QAEL,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,2BAA2B,CAC9D,KAAK,CAAC,uBAAuB,CAC9B,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2BAA2B,CACzB,eAAwB;QAExB,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE,CAAC;YACxC,0EAA0E;YAC1E,OAAO,eAA4C,CAAC;QACtD,CAAC;QACD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,IAAI,sCAAwB,CAChC,8CAA8C,OAAO,eAAe,EAAE,CACvE,CAAC;QACJ,CAAC;QACD,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,QAAQ,CAAC;YACd,KAAK,mBAAmB;gBACtB,OAAO,EAAC,OAAO,qDAAsC,EAAC,CAAC;YACzD,KAAK,SAAS,CAAC;YACf,KAAK,oBAAoB;gBACvB,OAAO,EAAC,OAAO,uDAAuC,EAAC,CAAC;YAC1D,KAAK,QAAQ;gBACX,OAAO,EAAC,OAAO,qDAAsC,EAAC,CAAC;YACzD;gBACE,MAAM,IAAI,sCAAwB,CAChC,+CAA+C,eAAe,EAAE,CACjE,CAAC;QACN,CAAC;IACH,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,MAA6B;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAEzE,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAEhD,MAAM,OAAO,GACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAEjE,OAAO;YACL,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE;gBACZ,GAAG,mBAAmB;gBACtB,mBAAmB,EAAE,mBAAmB,CAAC,mBAAmB,IAAI,KAAK;gBACrE,WAAW,EAAE,OAAO,CAAC,OAAO;gBAC5B,cAAc,EAAE,OAAO,CAAC,QAAQ;gBAChC,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,KAAK;gBACpB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAmC,EACnC,UAA2B,IAAI;QAE/B,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAChC,MAAM,CAAC,MAAmC,EAC1C,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,EACzB,OAAO,CACR,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAAmC,EACnC,UAA2B,IAAI;QAE/B,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAClC,MAAM,CAAC,MAAmC,EAC1C,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,EACzB,OAAO,CACR,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AA1ID,4CA0IC"}