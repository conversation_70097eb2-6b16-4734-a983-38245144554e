"use strict";
/**
 * Copyright 2021 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapperCdpClient = exports.CloseError = void 0;
const EventEmitter_js_1 = require("../utils/EventEmitter.js");
/** An error that will be thrown if/when the connection is closed. */
class CloseError extends Error {
}
exports.CloseError = CloseError;
/** Represents a high-level CDP connection to the browser. */
class MapperCdpClient extends EventEmitter_js_1.EventEmitter {
    #cdpConnection;
    #sessionId;
    constructor(cdpConnection, sessionId) {
        super();
        this.#cdpConnection = cdpConnection;
        this.#sessionId = sessionId;
    }
    get sessionId() {
        return this.#sessionId;
    }
    sendCommand(method, ...params) {
        return this.#cdpConnection.sendCommand(method, params[0], this.#sessionId);
    }
    isCloseError(error) {
        return error instanceof CloseError;
    }
}
exports.MapperCdpClient = MapperCdpClient;
//# sourceMappingURL=CdpClient.js.map