{"name": "bare-stream", "version": "2.6.1", "description": "Streaming data for JavaScript", "exports": {".": "./index.js", "./package": "./package.json", "./promises": "./promises.js", "./web": "./web.js", "./global": "./global.js"}, "files": ["index.js", "promises.js", "web.js", "global.js"], "scripts": {"test": "prettier . --check && bare test/all.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-stream.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-stream/issues"}, "homepage": "https://github.com/holepunchto/bare-stream#readme", "dependencies": {"streamx": "^2.21.0"}, "devDependencies": {"brittle": "^3.5.2", "prettier": "^3.3.3", "prettier-config-standard": "^7.0.0"}}