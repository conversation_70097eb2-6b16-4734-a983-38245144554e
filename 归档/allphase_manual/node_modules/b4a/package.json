{"name": "b4a", "version": "1.6.7", "description": "Bridging the gap between buffers and typed arrays", "main": "index.js", "files": ["browser.js", "index.js", "lib"], "browser": {"./index.js": "./browser.js"}, "scripts": {"test": "standard && brittle test/*.mjs"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/b4a.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/b4a/issues"}, "homepage": "https://github.com/holepunchto/b4a#readme", "devDependencies": {"brittle": "^3.5.2", "nanobench": "^3.0.0", "standard": "^17.1.0"}}