{"name": "bare-os", "version": "2.4.4", "description": "Operating system utilities for Javascript", "exports": {".": "./index.js", "./package": "./package.json", "./constants": "./lib/constants.js", "./errors": "./lib/errors.js"}, "files": ["index.js", "binding.c", "binding.js", "CMakeLists.txt", "lib", "prebuilds"], "addon": true, "scripts": {"test": "standard && bare test.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-os.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-os/issues"}, "homepage": "https://github.com/holepunchto/bare-os#readme", "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}}