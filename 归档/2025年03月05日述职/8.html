<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八大板块展示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(to bottom, #ffffff, #fff8f8, #ffffff);
            font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
            overflow: hidden;
        }
        .title {
            font-size: 52px;
            font-weight: bold;
            color: #d50000;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }
        .container {
            width: 90vw;
            height: 70vh; /* 调整高度以适应16:9比例 */
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            aspect-ratio: 16/9; /* 确保16:9的比例 */
        }
        .box {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 38px; /* 增大字体 */
            font-weight: bold;
            color: white;
            background-size: cover;
            background-position: center;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid rgba(213, 0, 0, 0.15);
        }
        .box:hover {
            transform: scale(1.03);
            box-shadow: 0 12px 25px rgba(213, 0, 0, 0.25);
        }
        .box::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4); /* 改回黑色半透明遮罩，但透明度调高 */
            border-radius: 13px;
        }
        .box span {
            position: relative;
            z-index: 2;
        }
        .box:nth-child(1) { background-image: url('images/construction.png'); }
        .box:nth-child(2) { background-image: url('images/technology.png'); }
        .box:nth-child(3) { background-image: url('images/research.png'); }
        .box:nth-child(4) { background-image: url('images/alliance.png'); }
        
        .box:nth-child(5) { background-image: url('images/academic.png'); }
        .box:nth-child(6) { background-image: url('images/doctor.png'); }
        .box:nth-child(7) { background-image: url('images/experience.png'); }
        .box:nth-child(8) { background-image: url('images/health.png'); }
        
        /* 添加底部装饰 */
        .footer {
            width: 100%;
            height: 40px;
            background: linear-gradient(to right, transparent, #d50000, transparent);
            margin-top: 20px;
            border-radius: 20px;
        }
        
        /* 添加喜庆装饰元素 */
        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .decoration::before,
        .decoration::after {
            content: "";
            position: absolute;
            width: 200px;
            height: 200px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23d50000" opacity="0.05"><path d="M12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2Z"/></svg>');
            background-repeat: no-repeat;
            background-size: contain;
        }
        
        .decoration::before {
            top: 5%;
            left: 5%;
            transform: rotate(-15deg);
        }
        
        .decoration::after {
            bottom: 5%;
            right: 5%;
            transform: rotate(15deg);
        }
        
        /* 确保在不同屏幕上保持16:9比例 */
        @media screen and (max-width: 1200px) {
            .title {
                font-size: 44px;
            }
            .box {
                font-size: 32px;
            }
        }
        
        @media screen and (max-width: 768px) {
            .title {
                font-size: 36px;
            }
            .box {
                font-size: 26px;
            }
        }
    </style>
</head>
<body>
    <div class="decoration"></div>
    <div class="title">呼吸与危重症医学科年终述职</div>
    <div class="container">
        <div class="box"><span>科室建设</span></div>
        <div class="box"><span>医疗技术</span></div>
        <div class="box"><span>临床研究</span></div>
        <div class="box"><span>专科联盟</span></div>
        
        <div class="box"><span>学术交流</span></div>
        <div class="box"><span>名医坐诊</span></div>
        <div class="box"><span>就医体验</span></div>
        <div class="box"><span>健康科普</span></div>
    </div>
    <div class="footer"></div>
</body>
</html>
